import { useRef } from "react";
import { SubmissionData } from "../App";
import { Logo, IconUser, IconArrowDown, IconArrow } from "./shared/Icons";
import { MedicalChartPage } from "./MedicalChartPage";
import { formatDate, getActiveTabLabel } from "./utils/managerReviewUtils";

interface ManagerReviewProps {
  submissionData: SubmissionData;
  onBackToDashboard: () => void;
}

export default function ManagerReview({ submissionData, onBackToDashboard }: ManagerReviewProps) {
  const pdfViewerRef = useRef<HTMLDivElement>(null);

  // Filter to only show selected AI highlights
  const selectedAiHighlights = submissionData.aiHighlights.filter(item => item.selected);

  const scrollToPage = (pageNumber: number) => {
    if (pdfViewerRef.current) {
      const pageElement = pdfViewerRef.current.querySelector(`[data-page="${pageNumber}"]`);
      if (pageElement) {
        pageElement.scrollIntoView({ 
          behavior: 'smooth',
          block: 'start'
        });
      }
    }
  };

  return (
    <div className="bg-background box-border content-stretch flex flex-col items-start justify-start p-0 relative size-full min-h-screen">
      <div className="bg-background box-border content-stretch flex flex-col min-h-screen items-start justify-start p-0 relative shrink-0 w-full">
        {/* Top Navigation */}
        <div className="bg-card box-border content-stretch flex flex-col h-16 sm:h-20 items-start justify-center p-0 relative shrink-0 w-full">
          <div aria-hidden="true" className="absolute border-b border-border inset-0 pointer-events-none" />
          <div className="basis-0 box-border content-stretch flex flex-row grow items-center justify-start min-h-px min-w-px p-0 relative shrink-0 w-full">
            <div className="basis-0 grow h-full min-h-px min-w-px relative shrink-0">
              <div className="flex flex-row items-center relative size-full">
                <div className="bg-clip-padding border-0 border-[transparent] border-solid box-border content-stretch flex flex-row items-center justify-start px-4 sm:px-[30px] py-3 relative size-full">
                  <div className="basis-0 box-border content-stretch flex flex-row gap-3 sm:gap-5 grow h-[46px] items-center justify-between min-h-px min-w-px p-0 relative shrink-0">
                    <div className="basis-0 box-border content-stretch flex flex-row gap-3 sm:gap-5 grow items-center justify-start min-h-px min-w-px p-0 relative shrink-0">
                      <Logo />
                    </div>
                    <div className="box-border content-stretch flex flex-row gap-2 sm:gap-4 items-center justify-end p-0 relative shrink-0">
                      <div className="box-border content-stretch flex flex-row gap-3 sm:gap-5 items-center justify-start px-2 sm:px-5 py-3 relative shrink-0">
                        <div className="box-border content-stretch flex flex-row gap-2 sm:gap-3 items-center justify-start p-0 relative shrink-0">
                          <p className="text-foreground hidden sm:block">Manager Review</p>
                          <div className="bg-primary/20 box-border content-stretch flex flex-row gap-2 items-center justify-center overflow-clip p-[8px] relative rounded-[120px] shrink-0">
                            <IconUser />
                          </div>
                          <IconArrowDown />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="basis-0 box-border content-stretch flex flex-col gap-4 sm:gap-5 grow items-start justify-start min-h-px min-w-px px-0 py-4 sm:py-5 relative shrink-0 w-full">
          <div className="relative shrink-0 w-full">
            <div className="relative size-full">
              <div className="box-border content-stretch flex flex-col gap-4 sm:gap-5 items-start justify-start px-4 sm:px-[30px] py-0 relative w-full">
                
                {/* Header */}
                <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between w-full">
                  <div className="flex flex-col gap-2">
                    <h1 className="text-foreground">Manager Review</h1>
                    <p className="text-muted-foreground">Verify the accuracy of submitted chart review</p>
                  </div>
                  <button
                    onClick={onBackToDashboard}
                    className="box-border content-stretch flex flex-row gap-2 items-center justify-start p-2 relative shrink-0 hover:opacity-80 transition-opacity cursor-pointer touch-target"
                  >
                    <IconArrow />
                    <span className="text-accent">Back to Dashboard</span>
                  </button>
                </div>

                {/* Submission Info */}
                <div className="bg-card relative rounded-lg shrink-0 w-full shadow-[var(--elevation-sm)]">
                  <div aria-hidden="true" className="absolute border border-border inset-0 pointer-events-none rounded-lg" />
                  <div className="flex flex-col justify-center relative size-full">
                    <div className="box-border content-stretch flex flex-col items-start justify-center p-4 sm:p-[20px] relative w-full">
                      <h2 className="text-foreground mb-4">Submission Details</h2>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 w-full">
                        <div>
                          <span className="text-muted-foreground">Submitted By:</span>
                          <p className="text-foreground font-[var(--font-weight-medium)]">{submissionData.submittedBy}</p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Submitted At:</span>
                          <p className="text-foreground font-[var(--font-weight-medium)]">{formatDate(submissionData.submittedAt)}</p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Measure:</span>
                          <p className="text-foreground font-[var(--font-weight-medium)]">CBP</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Patient Information */}
                <div className="bg-card relative rounded-lg shrink-0 w-full shadow-[var(--elevation-sm)]">
                  <div aria-hidden="true" className="absolute border border-border inset-0 pointer-events-none rounded-lg" />
                  <div className="flex flex-col justify-center relative size-full">
                    <div className="box-border content-stretch flex flex-col items-start justify-center p-4 sm:p-[20px] relative w-full">
                      <h2 className="text-foreground mb-4">Patient Information</h2>
                      <div className="grid grid-cols-6 gap-4 w-full">
                        <div>
                          <span className="text-muted-foreground">Member ID:</span>
                          <p className="text-foreground font-[var(--font-weight-medium)]">{submissionData.patientInfo.memberId}</p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Name:</span>
                          <p className="text-foreground font-[var(--font-weight-medium)]">{submissionData.patientInfo.name}</p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">DOB:</span>
                          <p className="text-foreground font-[var(--font-weight-medium)]">{submissionData.patientInfo.dob}</p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">LOB:</span>
                          <p className="text-foreground font-[var(--font-weight-medium)]">{submissionData.patientInfo.lob}</p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Provider:</span>
                          <p className="text-foreground font-[var(--font-weight-medium)]">{submissionData.patientInfo.provider}</p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">NPI:</span>
                          <p className="text-foreground font-[var(--font-weight-medium)]">{submissionData.patientInfo.npi}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Chart and Review Data Layout */}
                <div className="flex flex-col xl:flex-row gap-4 xl:gap-5 w-full">
                  
                  {/* Left Column - PDF Chart with Highlights */}
                  <div className="bg-card box-border content-stretch flex flex-col h-[600px] sm:h-[800px] xl:h-[1018px] items-start justify-start p-4 sm:p-[20px] relative rounded-lg shrink-0 w-full xl:flex-1 shadow-[var(--elevation-sm)]">
                    <div aria-hidden="true" className="absolute border border-border inset-0 pointer-events-none rounded-lg" />
                    
                    {/* Chart Header */}
                    <div className="bg-card border-b border-border flex flex-col sm:flex-row gap-3 items-start sm:items-center justify-between p-3 w-full mb-4 rounded-t-lg">
                      <h2 className="text-foreground font-[var(--font-weight-medium)]">Medical Chart with Primary User Highlights</h2>
                      {submissionData.manualHighlights.length > 0 && (
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                          <div className="w-3 h-3 bg-yellow-300 rounded"></div>
                          <span>{submissionData.manualHighlights.length} Manual Highlights</span>
                        </div>
                      )}
                    </div>
                    
                    <div
                      ref={pdfViewerRef}
                      className="bg-[#f5f5f5] flex-1 overflow-y-auto overflow-x-hidden relative shrink-0 w-full rounded-md"
                    >
                      {Array.from({ length: 8 }, (_, i) => i + 1).map(pageNumber => (
                        <MedicalChartPage 
                          key={pageNumber}
                          pageNumber={pageNumber}
                          manualHighlights={submissionData.manualHighlights}
                        />
                      ))}
                    </div>
                  </div>

                  {/* Right Column - Review Data */}
                  <div className="flex flex-col gap-4 xl:gap-5 w-full xl:w-1/2">
                    
                    {/* AI Highlights - Only show if there are selected highlights */}
                    {selectedAiHighlights.length > 0 && (
                      <div className="bg-card relative rounded-lg shrink-0 w-full shadow-[var(--elevation-sm)]">
                        <div aria-hidden="true" className="absolute border border-border inset-0 pointer-events-none rounded-lg" />
                        <div className="flex flex-col justify-center relative size-full">
                          <div className="box-border content-stretch flex flex-col items-start justify-center p-4 sm:p-[20px] relative w-full">
                            <h2 className="text-foreground mb-4">AI Highlights Review ({selectedAiHighlights.length} Selected)</h2>
                            
                            {/* AI Highlights Table */}
                            <div className="w-full overflow-x-auto">
                              <div className="border-b border-border pb-2 mb-4 min-w-[350px]">
                                <div className="grid grid-cols-5 gap-2 text-xs">
                                  <span className="text-muted-foreground font-[var(--font-weight-medium)]">Measure</span>
                                  <span className="text-muted-foreground font-[var(--font-weight-medium)]">DoS</span>
                                  <span className="text-muted-foreground font-[var(--font-weight-medium)]">Systolic</span>
                                  <span className="text-muted-foreground font-[var(--font-weight-medium)]">Diastolic</span>
                                  <span className="text-muted-foreground font-[var(--font-weight-medium)]">Page</span>
                                </div>
                              </div>
                              <div className="space-y-2 min-w-[350px]">
                                {selectedAiHighlights.map((item, index) => (
                                  <div key={index} className="grid grid-cols-5 gap-2 text-xs py-2">
                                    <span className="text-foreground">{item.measure}</span>
                                    <span className="text-foreground">{item.dos}</span>
                                    <span className="text-foreground">{item.systolic}</span>
                                    <span className="text-foreground">{item.diastolic}</span>
                                    <button 
                                      onClick={() => scrollToPage(item.page)}
                                      className="text-accent underline hover:text-accent/80 cursor-pointer transition-colors text-left touch-target"
                                    >
                                      {item.page}
                                    </button>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Manual Highlights */}
                    {submissionData.manualHighlights.length > 0 && (
                      <div className="bg-card relative rounded-lg shrink-0 w-full shadow-[var(--elevation-sm)]">
                        <div aria-hidden="true" className="absolute border border-border inset-0 pointer-events-none rounded-lg" />
                        <div className="flex flex-col justify-center relative size-full">
                          <div className="box-border content-stretch flex flex-col items-start justify-center p-4 sm:p-[20px] relative w-full">
                            <h2 className="text-foreground mb-4">Manual Highlights ({submissionData.manualHighlights.length})</h2>
                            <div className="space-y-2 w-full max-h-64 overflow-y-auto">
                              {submissionData.manualHighlights.map((highlight) => (
                                <div key={highlight.id} className="flex flex-row items-center justify-between text-xs bg-yellow-100 border border-yellow-200 rounded px-3 py-2">
                                  <div className="flex-1 truncate">
                                    <span className="text-foreground">{highlight.text}</span>
                                    <span className="text-muted-foreground ml-2">
                                      (Page{' '}
                                      <button 
                                        onClick={() => scrollToPage(highlight.pageNumber)}
                                        className="text-accent underline hover:text-accent/80 cursor-pointer transition-colors touch-target"
                                      >
                                        {highlight.pageNumber}
                                      </button>
                                      )
                                    </span>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Form Results */}
                    <div className="bg-card relative rounded-lg shrink-0 w-full shadow-[var(--elevation-sm)]">
                      <div aria-hidden="true" className="absolute border border-border inset-0 pointer-events-none rounded-lg" />
                      <div className="flex flex-col justify-center relative size-full">
                        <div className="box-border content-stretch flex flex-col items-start justify-center p-4 sm:p-[20px] relative w-full">
                          <h2 className="text-foreground mb-4">Submitted Results - {getActiveTabLabel(submissionData.formResults.activeTab)}</h2>
                          
                          <div className="space-y-4 w-full">
                            {submissionData.formResults.activeTab === 'inclusions' && (
                              <>
                                {/* Telehealth */}
                                <div className="flex flex-row gap-2 items-center">
                                  <div className={`w-5 h-5 rounded-sm border-2 flex items-center justify-center ${
                                    submissionData.formResults.telehealth ? 'bg-primary border-primary' : 'bg-transparent border-border'
                                  }`}>
                                    {submissionData.formResults.telehealth && (
                                      <svg className="w-3 h-3 text-primary-foreground" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                      </svg>
                                    )}
                                  </div>
                                  <span className="text-foreground">Telehealth</span>
                                </div>

                                {/* Blood Pressure Values and Date of Service - All in one line */}
                                <div className="grid grid-cols-3 gap-4">
                                  <div>
                                    <span className="text-muted-foreground">Systolic:</span>
                                    <p className="text-foreground font-[var(--font-weight-medium)]">{submissionData.formResults.systolic || 'Not provided'}</p>
                                  </div>
                                  <div>
                                    <span className="text-muted-foreground">Diastolic:</span>
                                    <p className="text-foreground font-[var(--font-weight-medium)]">{submissionData.formResults.diastolic || 'Not provided'}</p>
                                  </div>
                                  <div>
                                    <span className="text-muted-foreground">Date of Service:</span>
                                    <p className="text-foreground font-[var(--font-weight-medium)]">{submissionData.formResults.dateOfService || 'Not provided'}</p>
                                  </div>
                                </div>
                              </>
                            )}

                            {submissionData.formResults.activeTab === 'exclusions' && (
                              <>
                                {/* Exclusion Reasons */}
                                <div>
                                  <span className="text-muted-foreground">Exclusion Reasons:</span>
                                  {submissionData.formResults.exclusionReasons.length > 0 ? (
                                    <ul className="list-disc list-inside mt-1 space-y-1">
                                      {submissionData.formResults.exclusionReasons.map((reason, index) => (
                                        <li key={index} className="text-foreground text-xs">{reason}</li>
                                      ))}
                                    </ul>
                                  ) : (
                                    <p className="text-foreground font-[var(--font-weight-medium)]">None selected</p>
                                  )}
                                </div>

                                {/* Exclusion Date of Service */}
                                <div>
                                  <span className="text-muted-foreground">Date of Service:</span>
                                  <p className="text-foreground font-[var(--font-weight-medium)]">{submissionData.formResults.exclusionDateOfService || 'Not provided'}</p>
                                </div>
                              </>
                            )}

                            {submissionData.formResults.activeTab === 'none-found' && (
                              <>
                                {/* None Found Reasons */}
                                <div>
                                  <span className="text-muted-foreground">None Found Reasons:</span>
                                  {submissionData.formResults.noneFoundReasons.length > 0 ? (
                                    <ul className="list-disc list-inside mt-1 space-y-1">
                                      {submissionData.formResults.noneFoundReasons.map((reason, index) => (
                                        <li key={index} className="text-foreground text-xs">{reason}</li>
                                      ))}
                                    </ul>
                                  ) : (
                                    <p className="text-foreground font-[var(--font-weight-medium)]">None selected</p>
                                  )}
                                </div>
                              </>
                            )}

                            {/* Notes */}
                            <div>
                              <span className="text-muted-foreground">Notes:</span>
                              <div className="bg-secondary/30 rounded-lg p-3 mt-1">
                                <p className="text-foreground text-[12px] whitespace-pre-wrap">
                                  {submissionData.formResults.notes || 'No notes provided'}
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex flex-col sm:flex-row gap-3 justify-end w-full">
                      <button className="bg-destructive hover:bg-destructive/90 text-destructive-foreground px-6 py-3 rounded-lg transition-colors cursor-pointer touch-target">
                        Reject Review
                      </button>
                      <button className="bg-primary hover:bg-[#468CE7] text-primary-foreground px-6 py-3 rounded-lg transition-colors cursor-pointer touch-target">
                        Approve Review
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}