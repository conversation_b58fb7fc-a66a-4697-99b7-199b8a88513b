import svgPaths from "./svg-r3wkftmnvk";

function RightCorner() {
  return (
    <div
      className="basis-0 grow h-full min-h-px min-w-px shrink-0"
      data-name="right corner"
    />
  );
}

function Stack() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-2.5 items-center justify-start p-0 relative shrink-0 w-full"
      data-name="stack"
    >
      <div className="font-['Urbane:Demi_Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[20px] text-left text-nowrap">
        <p className="block leading-[32px] whitespace-pre">Results</p>
      </div>
      <div className="basis-0 flex flex-row grow items-center self-stretch shrink-0">
        <RightCorner />
      </div>
    </div>
  );
}

function Frame831() {
  return (
    <div className="box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative rounded-md shrink-0">
      <div className="font-['Urbane:Demi_Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#547996] text-[14px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Inclusions</p>
      </div>
    </div>
  );
}

function MenuItem() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-full items-center justify-start px-2 py-1 relative shrink-0"
      data-name="Menu Item"
    >
      <Frame831 />
    </div>
  );
}

function MenuItem1() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-4 h-full items-start justify-start p-0 relative shrink-0"
      data-name="menu item"
    >
      <MenuItem />
    </div>
  );
}

function Frame832() {
  return (
    <div className="box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative rounded-md shrink-0">
      <div className="font-['Urbane:Demi_Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#3870b8] text-[14px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Exclusions</p>
      </div>
    </div>
  );
}

function MenuItem2() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-full items-center justify-start px-2 py-1 relative shrink-0"
      data-name="Menu Item"
    >
      <Frame832 />
    </div>
  );
}

function MenuItem3() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-4 h-full items-start justify-start p-0 relative shrink-0"
      data-name="menu item"
    >
      <div
        aria-hidden="true"
        className="absolute border-[#3870b8] border-[0px_0px_1px] border-solid inset-0 pointer-events-none"
      />
      <MenuItem2 />
    </div>
  );
}

function Frame833() {
  return (
    <div className="box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative rounded-md shrink-0">
      <div className="font-['Urbane:Demi_Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#547996] text-[14px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">None found</p>
      </div>
    </div>
  );
}

function MenuItem4() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-full items-center justify-start px-2 py-1 relative shrink-0"
      data-name="Menu Item"
    >
      <Frame833 />
    </div>
  );
}

function MenuItem5() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-4 h-full items-start justify-start p-0 relative shrink-0"
      data-name="menu item"
    >
      <MenuItem4 />
    </div>
  );
}

function Menu() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-4 h-full items-center justify-start p-0 relative shrink-0"
      data-name="menu"
    >
      <MenuItem1 />
      <MenuItem3 />
      <MenuItem5 />
    </div>
  );
}

function Box() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-row grow items-center justify-start min-h-px min-w-px p-0 relative shrink-0 w-full"
      data-name="box"
    >
      <Menu />
    </div>
  );
}

function Bradcrumb() {
  return (
    <div className="h-10 relative shrink-0" data-name="bradcrumb">
      <div className="bg-clip-padding border-0 border-[transparent] border-solid box-border content-stretch flex flex-col h-10 items-start justify-center p-0 relative">
        <Box />
      </div>
    </div>
  );
}

function SecondMenu() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row items-center justify-start pb-px pt-0 px-0 relative shrink-0 w-full"
      data-name="second menu"
    >
      <div
        aria-hidden="true"
        className="absolute border-[#d9e1e7] border-[0px_0px_1px] border-solid inset-0 pointer-events-none"
      />
      <Bradcrumb />
    </div>
  );
}

function Menu1() {
  return (
    <div
      className="box-border content-stretch flex flex-col h-[41px] items-start justify-start p-0 relative shrink-0 w-full"
      data-name="menu"
    >
      <SecondMenu />
    </div>
  );
}

function Frame929() {
  return (
    <div className="basis-0 box-border content-stretch flex flex-col gap-3 grow items-start justify-start min-h-px min-w-px p-0 relative shrink-0">
      <Stack />
      <Menu1 />
    </div>
  );
}

function Head() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-5 items-center justify-start pb-3 pt-0 px-0 relative shrink-0 w-full"
      data-name="head"
    >
      <Frame929 />
    </div>
  );
}

function IconRight() {
  return (
    <div className="h-2 relative w-3.5" data-name="icon_right">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 14 8"
      >
        <g id="icon_right">
          <path
            d={svgPaths.p37ae0a00}
            id="Vector"
            stroke="var(--stroke-0, #547996)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
        </g>
      </svg>
    </div>
  );
}

function Row() {
  return (
    <div
      className="bg-[#ffffff] relative rounded-md shrink-0 w-full"
      data-name="row"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 items-center justify-start px-3 py-2 relative w-full">
          <div className="basis-0 font-['Urbane:Light',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[#547996] text-[12px] text-left">
            <p className="block leading-[20px]">Select</p>
          </div>
          <div className="flex h-[14px] items-center justify-center relative shrink-0 w-[8px]">
            <div className="flex-none rotate-[270deg]">
              <IconRight />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function DropdownExclusion() {
  return (
    <div
      className="bg-[#ffffff] h-11 relative rounded-[10px] shrink-0 w-full"
      data-name="Dropdown-exclusion"
    >
      <div className="flex flex-col items-center justify-center overflow-clip relative size-full">
        <div className="box-border content-stretch flex flex-col h-11 items-center justify-center p-[4px] relative w-full">
          <Row />
        </div>
      </div>
      <div
        aria-hidden="true"
        className="absolute border border-[#d9e1e7] border-solid inset-0 pointer-events-none rounded-[10px]"
      />
    </div>
  );
}

function Frame939() {
  return (
    <div className="box-border content-stretch flex flex-col gap-1 items-start justify-start p-0 relative shrink-0 w-full">
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#547996] text-[10px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Reasoning</p>
      </div>
      <DropdownExclusion />
    </div>
  );
}

function IconInterface3() {
  return (
    <div className="relative shrink-0 size-6" data-name="icon_interface">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 24 24"
      >
        <g id="icon_interface">
          <path
            d={svgPaths.p393d4de0}
            id="Vector"
            stroke="var(--stroke-0, #547996)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
        </g>
      </svg>
    </div>
  );
}

function Row1() {
  return (
    <div
      className="bg-[#ffffff] relative rounded-md shrink-0 w-full"
      data-name="row"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 items-center justify-start px-3 py-2 relative w-full">
          <div className="basis-0 font-['Urbane:Light',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[#547996] text-[12px] text-left">
            <p className="block leading-[20px]">MM/DD/YY</p>
          </div>
          <IconInterface3 />
        </div>
      </div>
    </div>
  );
}

function Calendar() {
  return (
    <div
      className="bg-[#ffffff] h-12 relative rounded-[10px] shrink-0 w-full"
      data-name="Calendar"
    >
      <div className="flex flex-col items-center justify-center overflow-clip relative size-full">
        <div className="box-border content-stretch flex flex-col h-12 items-center justify-center p-[4px] relative w-full">
          <Row1 />
        </div>
      </div>
      <div
        aria-hidden="true"
        className="absolute border border-[#d9e1e7] border-solid inset-0 pointer-events-none rounded-[10px]"
      />
    </div>
  );
}

function Frame940() {
  return (
    <div className="box-border content-stretch flex flex-col gap-1 items-start justify-start p-0 relative shrink-0 w-full">
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#547996] text-[10px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Date of service</p>
      </div>
      <Calendar />
    </div>
  );
}

function Text() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-row gap-0.5 grow items-start justify-start min-h-px min-w-px p-0 relative shrink-0 w-full"
      data-name="Text"
    >
      <div className="basis-0 flex flex-col font-['Urbane:Light',_sans-serif] grow h-[11.185px] justify-end leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[#547996] text-[12px] text-left">
        <p className="block leading-[16px]">Notes</p>
      </div>
    </div>
  );
}

function InputField() {
  return (
    <div
      className="bg-[#ffffff] h-[88px] relative rounded-lg shrink-0 w-full"
      data-name="Input Field"
    >
      <div
        aria-hidden="true"
        className="absolute border border-[#d9e1e7] border-solid inset-0 pointer-events-none rounded-lg"
      />
      <div className="relative size-full">
        <div className="box-border content-stretch flex flex-col gap-4 h-[88px] items-start justify-start pb-1 pt-2 px-3 relative w-full">
          <Text />
        </div>
      </div>
    </div>
  );
}

function TextAreaField() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-col gap-2 grow items-start justify-start min-h-px min-w-px p-0 relative shrink-0"
      data-name="Text Area Field"
    >
      <InputField />
    </div>
  );
}

function Notes() {
  return (
    <div
      className="box-border content-stretch flex flex-row items-start justify-start p-0 relative shrink-0 w-full"
      data-name="notes"
    >
      <TextAreaField />
    </div>
  );
}

function Frame941() {
  return (
    <div className="box-border content-stretch flex flex-col gap-1 items-start justify-start p-0 relative shrink-0 w-full">
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#547996] text-[10px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Notes</p>
      </div>
      <Notes />
    </div>
  );
}

function Frame916() {
  return (
    <div className="box-border content-stretch flex flex-col gap-3 items-start justify-start p-0 relative shrink-0 w-full">
      <Frame939 />
      <Frame940 />
      <Frame941 />
    </div>
  );
}

export default function Notes1() {
  return (
    <div
      className="bg-[#ffffff] relative rounded-lg size-full"
      data-name="Notes"
    >
      <div
        aria-hidden="true"
        className="absolute border border-[#f1f5f7] border-solid inset-0 pointer-events-none rounded-lg"
      />
      <div className="flex flex-col justify-center relative size-full">
        <div className="box-border content-stretch flex flex-col items-start justify-center p-[20px] relative size-full">
          <Head />
          <Frame916 />
        </div>
      </div>
    </div>
  );
}