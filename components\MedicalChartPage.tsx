import { medicalChartPages } from "./constants/medicalChartData";

interface ManualHighlight {
  id: string;
  pageNumber: number;
  lineIndex: number;
  startIndex: number;
  endIndex: number;
  text: string;
  timestamp: number;
}

interface AIHighlight {
  measure: string;
  dos: string;
  systolic: string;
  diastolic: string;
  page: number;
  selected: boolean;
}

interface MedicalChartPageProps {
  pageNumber: number;
  manualHighlights?: ManualHighlight[];
  aiHighlights?: AIHighlight[];
  highlightMode?: boolean;
  onTextHighlight?: (pageNumber: number, text: string) => void;
  searchTerm?: string;
}

export function MedicalChartPage({ 
  pageNumber, 
  manualHighlights = [], 
  aiHighlights = [],
  highlightMode = false,
  onTextHighlight,
  searchTerm = ''
}: MedicalChartPageProps) {
  const pageData = medicalChartPages[pageNumber - 1];
  
  if (!pageData) {
    return (
      <div data-page={pageNumber} className="bg-white p-8 m-4 rounded shadow-sm min-h-[800px] flex items-center justify-center">
        <p className="text-muted-foreground">Page {pageNumber} - Content not available</p>
      </div>
    );
  }

  const handleTextSelection = () => {
    if (!highlightMode || !onTextHighlight) return;
    
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const selectedText = selection.toString().trim();
      if (selectedText) {
        onTextHighlight(pageNumber, selectedText);
        selection.removeAllRanges(); // Clear selection after highlighting
      }
    }
  };

  const highlightSearchTerm = (text: string) => {
    if (!searchTerm.trim()) return text;
    
    const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => {
      if (regex.test(part)) {
        return <mark key={index} className="bg-yellow-300 px-1 rounded">{part}</mark>;
      }
      return part;
    });
  };

  const isLineManuallyHighlighted = (lineIndex: number) => {
    return manualHighlights.some(highlight => 
      highlight.pageNumber === pageNumber && highlight.lineIndex === lineIndex
    );
  };

  const renderLineWithAIHighlights = (line: string, lineIndex: number) => {
    // Find AI highlights for this page
    const pageAIHighlights = aiHighlights.filter(ai => ai.page === pageNumber);
    
    // Check if this line contains AI highlight markers
    let processedLine = line;
    let hasAIHighlight = false;
    
    pageAIHighlights.forEach(aiHighlight => {
      // Look for blood pressure patterns in the line
      const bpPattern = new RegExp(`(${aiHighlight.systolic}/${aiHighlight.diastolic}\\s*mmHg)`, 'gi');
      if (bpPattern.test(line)) {
        hasAIHighlight = true;
        processedLine = line.replace(bpPattern, (match) => {
          return `<AI_HIGHLIGHT data-selected='${aiHighlight.selected}'>${match}</AI_HIGHLIGHT>`;
        });
      }
    });

    // Also check for generic BP readings marked with arrows
    if (line.includes('←') && line.toLowerCase().includes('blood pressure')) {
      hasAIHighlight = true;
      processedLine = line.replace(/(Blood Pressure: \d+\/\d+ mmHg)\s*←/gi, (match, bpReading) => {
        // Try to match with existing AI highlights
        const matchingAI = pageAIHighlights.find(ai => 
          bpReading.includes(`${ai.systolic}/${ai.diastolic}`)
        );
        const isSelected = matchingAI ? matchingAI.selected : false;
        return `<AI_HIGHLIGHT data-selected='${isSelected}'>${bpReading}</AI_HIGHLIGHT>`;
      });
    }

    // Split the processed line and render with appropriate highlighting
    const parts = processedLine.split(/(<AI_HIGHLIGHT[^>]*>.*?<\/AI_HIGHLIGHT>)/);
    
    return parts.map((part, partIndex) => {
      if (part.startsWith('<AI_HIGHLIGHT')) {
        // Extract the selection state and content
        const selectedMatch = part.match(/data-selected='([^']*)'/) || [null, 'false'];
        const isSelected = selectedMatch[1] === 'true';
        const contentMatch = part.match(/<AI_HIGHLIGHT[^>]*>(.*?)<\/AI_HIGHLIGHT>/);
        const content = contentMatch ? contentMatch[1] : part.replace(/<\/?AI_HIGHLIGHT[^>]*>/g, '');

        return (
          <span
            key={partIndex}
            className={`inline-block px-2 py-1 rounded-md transition-all ${
              isSelected 
                ? 'bg-blue-200 border-2 border-blue-400 text-blue-900' 
                : 'bg-blue-100 border border-blue-300 text-blue-800'
            }`}
            title="AI Highlighted Content"
          >
            {highlightSearchTerm(content)}
          </span>
        );
      }
      
      return <span key={partIndex}>{highlightSearchTerm(part)}</span>;
    });
  };

  return (
    <div 
      data-page={pageNumber} 
      className="bg-white p-8 m-4 rounded shadow-sm min-h-[800px] relative"
      onMouseUp={handleTextSelection}
      style={{ userSelect: highlightMode ? 'text' : 'none' }}
    >
      {/* Page Header */}
      <div className="flex justify-between items-center mb-6 pb-2 border-b border-border">
        <div>
          <h3 className="text-foreground font-[var(--font-weight-demi-bold)]">{pageData.title}</h3>
          <p className="text-muted-foreground text-xs">Page {pageNumber} of 8</p>
        </div>
        <div className="text-muted-foreground text-xs">
          {pageData.date}
        </div>
      </div>

      {/* Page Content */}
      <div className="space-y-4">
        {pageData.content.map((line, lineIndex) => {
          const manuallyHighlighted = isLineManuallyHighlighted(lineIndex);
          return (
            <div
              key={lineIndex}
              className={`p-2 rounded transition-colors ${
                manuallyHighlighted 
                  ? 'bg-yellow-200 border-l-4 border-yellow-400' 
                  : 'hover:bg-secondary/30'
              } ${highlightMode ? 'cursor-text' : 'cursor-default'}`}
            >
              <p className="text-foreground text-xs leading-relaxed">
                {renderLineWithAIHighlights(line, lineIndex)}
              </p>
            </div>
          );
        })}
      </div>

      {/* Manual Highlights Indicator */}
      {manualHighlights.filter(h => h.pageNumber === pageNumber).length > 0 && (
        <div className="absolute top-4 right-4 bg-yellow-400 text-black px-2 py-1 rounded-full text-xs font-[var(--font-weight-medium)]">
          {manualHighlights.filter(h => h.pageNumber === pageNumber).length} manual highlight{manualHighlights.filter(h => h.pageNumber === pageNumber).length !== 1 ? 's' : ''}
        </div>
      )}

      {/* AI Highlights Indicator */}
      {aiHighlights.filter(ai => ai.page === pageNumber).length > 0 && (
        <div className="absolute top-4 right-20 bg-blue-400 text-white px-2 py-1 rounded-full text-xs font-[var(--font-weight-medium)]">
          {aiHighlights.filter(ai => ai.page === pageNumber).length} AI highlight{aiHighlights.filter(ai => ai.page === pageNumber).length !== 1 ? 's' : ''}
        </div>
      )}

      {/* Search Results Indicator */}
      {searchTerm && pageData.content.some(line => line.toLowerCase().includes(searchTerm.toLowerCase())) && (
        <div className="absolute top-4 left-4 bg-accent text-accent-foreground px-2 py-1 rounded-full text-xs font-[var(--font-weight-medium)]">
          Search match
        </div>
      )}
    </div>
  );
}