import svgPaths from "./svg-xi7i9jsjra";
import imgStellarusLogo from "figma:asset/abb0be6be146caf56aaaa0a625c81daa292fb52d.png";

function Logo() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 items-center justify-start px-5 py-2.5 relative shrink-0 w-60"
      data-name="logo"
    >
      <div
        className="bg-center bg-cover bg-no-repeat h-[37.397px] shrink-0 w-[150px]"
        data-name="stellarus-logo"
        style={{ backgroundImage: `url('${imgStellarusLogo}')` }}
      />
    </div>
  );
}

function Frame840() {
  return (
    <div className="basis-0 box-border content-stretch flex flex-row gap-5 grow items-center justify-start min-h-px min-w-px p-0 relative shrink-0">
      <Logo />
    </div>
  );
}

function IconUser() {
  return (
    <div className="relative shrink-0 size-5" data-name="icon_user">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 20 20"
      >
        <g id="profile-circle">
          <path
            d={svgPaths.p3aba0f00}
            id="Vector"
            stroke="var(--stroke-0, #3870B8)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
          <path
            d={svgPaths.p3e3a1300}
            id="Vector_2"
            stroke="var(--stroke-0, #3870B8)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
          <path
            d={svgPaths.p1246af00}
            id="Vector_3"
            stroke="var(--stroke-0, #3870B8)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
          <g id="Vector_4" opacity="0"></g>
        </g>
      </svg>
    </div>
  );
}

function Base() {
  return (
    <div
      className="bg-[rgba(56,112,184,0.2)] box-border content-stretch flex flex-row gap-2 items-center justify-center overflow-clip p-[8px] relative rounded-[120px] shrink-0"
      data-name="base"
    >
      <IconUser />
    </div>
  );
}

function IconArrowDown() {
  return (
    <div className="relative shrink-0 size-6" data-name="icon_arrow_down">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 24 24"
      >
        <g id="icon_arrow_down">
          <path
            d={svgPaths.p242a98e0}
            id="Vector"
            stroke="var(--stroke-0, #809FB8)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
        </g>
      </svg>
    </div>
  );
}

function Frame838() {
  return (
    <div className="box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0">
      <div className="flex flex-col font-['Urbane:Light',_sans-serif] justify-end leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Jane Chu</p>
      </div>
      <Base />
      <IconArrowDown />
    </div>
  );
}

function MenuItem() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-5 items-center justify-start px-5 py-3 relative shrink-0"
      data-name="Menu Item"
    >
      <Frame838 />
    </div>
  );
}

function Frame839() {
  return (
    <div className="box-border content-stretch flex flex-row gap-4 items-center justify-end p-0 relative shrink-0">
      <MenuItem />
    </div>
  );
}

function Frame() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-row grow h-[46px] items-center justify-between min-h-px min-w-px p-0 relative shrink-0"
      data-name="frame"
    >
      <Frame840 />
      <Frame839 />
    </div>
  );
}

function Box() {
  return (
    <div
      className="basis-0 grow h-full min-h-px min-w-px relative shrink-0"
      data-name="box"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="bg-clip-padding border-0 border-[transparent] border-solid box-border content-stretch flex flex-row items-center justify-start px-[30px] py-3 relative size-full">
          <Frame />
        </div>
      </div>
    </div>
  );
}

function Menu() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-row grow items-center justify-start min-h-px min-w-px p-0 relative shrink-0 w-full"
      data-name="menu"
    >
      <Box />
    </div>
  );
}

function Topbar() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-col h-20 items-start justify-center p-0 relative shrink-0 w-[1440px]"
      data-name="Topbar"
    >
      <div
        aria-hidden="true"
        className="absolute border-[#f1f5f7] border-[0px_0px_1px] border-solid inset-0 pointer-events-none"
      />
      <Menu />
    </div>
  );
}

function Menu1() {
  return (
    <div
      className="box-border content-stretch flex flex-col items-start justify-start p-0 shrink-0 sticky top-0 w-full"
      data-name="Menu"
    >
      <Topbar />
    </div>
  );
}

function RightCorner() {
  return (
    <div
      className="basis-0 grow h-full min-h-px min-w-px shrink-0"
      data-name="right corner"
    />
  );
}

function Stack() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-row gap-2.5 grow items-center justify-start min-h-px min-w-px p-0 relative shrink-0"
      data-name="stack"
    >
      <div className="font-['Urbane:Demi_Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[20px] text-left text-nowrap">
        <p className="block leading-[1.6] whitespace-pre">Assigned charts</p>
      </div>
      <div className="basis-0 flex flex-row grow items-center self-stretch shrink-0">
        <RightCorner />
      </div>
    </div>
  );
}

function IconArrow() {
  return (
    <div className="relative shrink-0 size-5" data-name="icon_arrow">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 20 20"
      >
        <g id="icon_arrow">
          <path
            d={svgPaths.p1efb6980}
            id="Vector"
            stroke="var(--stroke-0, #17181A)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
          <g id="Group">
            <path
              d={svgPaths.p70df800}
              id="Vector_2"
              stroke="var(--stroke-0, #17181A)"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="1.5"
            />
            <path
              d={svgPaths.p14f8e060}
              id="Vector_3"
              stroke="var(--stroke-0, #17181A)"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="1.5"
            />
            <path
              d={svgPaths.p7847000}
              id="Vector_4"
              stroke="var(--stroke-0, #17181A)"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="1.5"
            />
            <path
              d={svgPaths.p29617600}
              id="Vector_5"
              stroke="var(--stroke-0, #17181A)"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="1.5"
            />
          </g>
          <g id="Vector_6" opacity="0"></g>
        </g>
      </svg>
    </div>
  );
}

function ButtonBase() {
  return (
    <div
      className="bg-[#ffffff] relative rounded-lg shrink-0"
      data-name="_Button base"
    >
      <div className="box-border content-stretch flex flex-row gap-2 items-center justify-center overflow-clip px-3.5 py-2 relative">
        <IconArrow />
        <div className="flex flex-col font-['Urbane:Medium',_sans-serif] justify-center leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
          <p className="block leading-[20px] whitespace-pre">Refresh charts</p>
        </div>
      </div>
      <div
        aria-hidden="true"
        className="absolute border border-[#d9e1e7] border-solid inset-0 pointer-events-none rounded-lg"
      />
    </div>
  );
}

function Button() {
  return (
    <div
      className="box-border content-stretch flex flex-row items-start justify-start p-0 relative rounded-lg shrink-0"
      data-name="Button"
    >
      <ButtonBase />
    </div>
  );
}

function Head() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-5 items-center justify-start pb-5 pt-0 px-0 relative shrink-0 w-full"
      data-name="head"
    >
      <Stack />
      <Button />
    </div>
  );
}

function TableItem() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start p-0 relative shrink-0"
      data-name="Table Item"
    >
      <div className="font-['Urbane:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Member ID</p>
      </div>
    </div>
  );
}

function HeaderItem() {
  return (
    <div className="relative shrink-0 w-full" data-name="header item">
      <div
        aria-hidden="true"
        className="absolute border-[#f1f5f7] border-[0px_0px_1px] border-solid inset-0 pointer-events-none"
      />
      <div className="relative size-full">
        <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start px-3 py-0 relative w-full">
          <TableItem />
        </div>
      </div>
    </div>
  );
}

function TableItem1() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-[13px] relative shrink-0"
      data-name="Table Item"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">55820474</p>
      </div>
    </div>
  );
}

function TableItem2() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-[13px] relative shrink-0"
      data-name="Table Item"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">302274401</p>
      </div>
    </div>
  );
}

function TableItem3() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-[13px] relative shrink-0"
      data-name="Table Item"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">7729471914</p>
      </div>
    </div>
  );
}

function Column() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-col grow items-start justify-start min-h-px min-w-px p-0 relative shrink-0"
      data-name="column"
    >
      <HeaderItem />
      <TableItem1 />
      <TableItem2 />
      <TableItem3 />
    </div>
  );
}

function TableItem4() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start p-0 relative shrink-0"
      data-name="Table Item"
    >
      <div className="font-['Urbane:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">First name</p>
      </div>
    </div>
  );
}

function HeaderItem1() {
  return (
    <div className="relative shrink-0 w-full" data-name="header item">
      <div
        aria-hidden="true"
        className="absolute border-[#f1f5f7] border-[0px_0px_1px] border-solid inset-0 pointer-events-none"
      />
      <div className="relative size-full">
        <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start px-3 py-0 relative w-full">
          <TableItem4 />
        </div>
      </div>
    </div>
  );
}

function IconText() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0"
      data-name="icon text"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">John</p>
      </div>
    </div>
  );
}

function TableItem5() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-2.5 relative shrink-0"
      data-name="Table Item"
    >
      <IconText />
    </div>
  );
}

function IconText1() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0"
      data-name="icon text"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Alma</p>
      </div>
    </div>
  );
}

function TableItem6() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-2.5 relative shrink-0"
      data-name="Table Item"
    >
      <IconText1 />
    </div>
  );
}

function IconText2() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0"
      data-name="icon text"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Joanne</p>
      </div>
    </div>
  );
}

function TableItem7() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-2.5 relative shrink-0"
      data-name="Table Item"
    >
      <IconText2 />
    </div>
  );
}

function Column1() {
  return (
    <div
      className="box-border content-stretch flex flex-col items-start justify-start p-0 relative shrink-0 w-[107px]"
      data-name="column"
    >
      <HeaderItem1 />
      <TableItem5 />
      <TableItem6 />
      <TableItem7 />
    </div>
  );
}

function TableItem8() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start p-0 relative shrink-0"
      data-name="Table Item"
    >
      <div className="font-['Urbane:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Last name</p>
      </div>
    </div>
  );
}

function HeaderItem2() {
  return (
    <div className="relative shrink-0 w-full" data-name="header item">
      <div
        aria-hidden="true"
        className="absolute border-[#f1f5f7] border-[0px_0px_1px] border-solid inset-0 pointer-events-none"
      />
      <div className="relative size-full">
        <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start px-3 py-0 relative w-full">
          <TableItem8 />
        </div>
      </div>
    </div>
  );
}

function IconText3() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0"
      data-name="icon text"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Dey</p>
      </div>
    </div>
  );
}

function TableItem9() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-2.5 relative shrink-0"
      data-name="Table Item"
    >
      <IconText3 />
    </div>
  );
}

function IconText4() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0"
      data-name="icon text"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left w-[78px]">
        <p className="block leading-[20px]">Anders</p>
      </div>
    </div>
  );
}

function TableItem10() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-2.5 relative shrink-0"
      data-name="Table Item"
    >
      <IconText4 />
    </div>
  );
}

function IconText5() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0"
      data-name="icon text"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Smith</p>
      </div>
    </div>
  );
}

function TableItem11() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-2.5 relative shrink-0"
      data-name="Table Item"
    >
      <IconText5 />
    </div>
  );
}

function Column2() {
  return (
    <div
      className="box-border content-stretch flex flex-col items-start justify-start p-0 relative shrink-0 w-[106px]"
      data-name="column"
    >
      <HeaderItem2 />
      <TableItem9 />
      <TableItem10 />
      <TableItem11 />
    </div>
  );
}

function TableItem12() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start p-0 relative shrink-0"
      data-name="Table Item"
    >
      <div className="font-['Urbane:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Middle name</p>
      </div>
    </div>
  );
}

function HeaderItem3() {
  return (
    <div className="relative shrink-0 w-full" data-name="header item">
      <div
        aria-hidden="true"
        className="absolute border-[#f1f5f7] border-[0px_0px_1px] border-solid inset-0 pointer-events-none"
      />
      <div className="relative size-full">
        <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start px-3 py-0 relative w-full">
          <TableItem12 />
        </div>
      </div>
    </div>
  );
}

function IconText6() {
  return (
    <div className="bg-[#ffffff] h-1 shrink-0 w-0" data-name="icon text" />
  );
}

function TableItem13() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-2.5 relative shrink-0"
      data-name="Table Item"
    >
      <IconText6 />
    </div>
  );
}

function IconText7() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0"
      data-name="icon text"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left w-[78px]">
        <p className="block leading-[20px]">G</p>
      </div>
    </div>
  );
}

function TableItem14() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-2.5 relative shrink-0"
      data-name="Table Item"
    >
      <IconText7 />
    </div>
  );
}

function Column3() {
  return (
    <div
      className="box-border content-stretch flex flex-col items-start justify-start p-0 relative shrink-0 w-[102px]"
      data-name="column"
    >
      <HeaderItem3 />
      <TableItem13 />
      <TableItem14 />
      <TableItem13 />
    </div>
  );
}

function TableItem16() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start p-0 relative shrink-0"
      data-name="Table Item"
    >
      <div className="font-['Urbane:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">DOB</p>
      </div>
    </div>
  );
}

function HeaderItem4() {
  return (
    <div className="relative shrink-0 w-full" data-name="header item">
      <div
        aria-hidden="true"
        className="absolute border-[#f1f5f7] border-[0px_0px_1px] border-solid inset-0 pointer-events-none"
      />
      <div className="relative size-full">
        <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start px-3 py-0 relative w-full">
          <TableItem16 />
        </div>
      </div>
    </div>
  );
}

function IconText9() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0"
      data-name="icon text"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">01/05/1972</p>
      </div>
    </div>
  );
}

function TableItem17() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-2.5 relative shrink-0"
      data-name="Table Item"
    >
      <IconText9 />
    </div>
  );
}

function IconText10() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0"
      data-name="icon text"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">12/15/1953</p>
      </div>
    </div>
  );
}

function TableItem18() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-2.5 relative shrink-0"
      data-name="Table Item"
    >
      <IconText10 />
    </div>
  );
}

function IconText11() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0"
      data-name="icon text"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">06/30/1951</p>
      </div>
    </div>
  );
}

function TableItem19() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-2.5 relative shrink-0"
      data-name="Table Item"
    >
      <IconText11 />
    </div>
  );
}

function Column4() {
  return (
    <div
      className="box-border content-stretch flex flex-col items-start justify-start p-0 relative shrink-0 w-[99px]"
      data-name="column"
    >
      <HeaderItem4 />
      <TableItem17 />
      <TableItem18 />
      <TableItem19 />
    </div>
  );
}

function TableItem20() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start p-0 relative shrink-0"
      data-name="Table Item"
    >
      <div className="font-['Urbane:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left w-[27px]">
        <p className="block leading-[20px]">LOB</p>
      </div>
    </div>
  );
}

function HeaderItem5() {
  return (
    <div className="relative shrink-0 w-full" data-name="header item">
      <div
        aria-hidden="true"
        className="absolute border-[#f1f5f7] border-[0px_0px_1px] border-solid inset-0 pointer-events-none"
      />
      <div className="relative size-full">
        <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start px-3 py-0 relative w-full">
          <TableItem20 />
        </div>
      </div>
    </div>
  );
}

function IconText12() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0"
      data-name="icon text"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">MA HMO</p>
      </div>
    </div>
  );
}

function TableItem21() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-2.5 relative shrink-0"
      data-name="Table Item"
    >
      <IconText12 />
    </div>
  );
}

function Column5() {
  return (
    <div
      className="box-border content-stretch flex flex-col items-start justify-start p-0 relative shrink-0 w-[100px]"
      data-name="column"
    >
      <HeaderItem5 />
      {[...Array(3).keys()].map((_, i) => (
        <TableItem21 key={i} />
      ))}
    </div>
  );
}

function TableItem24() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start p-0 relative shrink-0"
      data-name="Table Item"
    >
      <div className="font-['Urbane:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Measure</p>
      </div>
    </div>
  );
}

function HeaderItem6() {
  return (
    <div className="relative shrink-0 w-full" data-name="header item">
      <div
        aria-hidden="true"
        className="absolute border-[#f1f5f7] border-[0px_0px_1px] border-solid inset-0 pointer-events-none"
      />
      <div className="relative size-full">
        <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start px-3 py-0 relative w-full">
          <TableItem24 />
        </div>
      </div>
    </div>
  );
}

function IconText15() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0"
      data-name="icon text"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left w-[25px]">
        <p className="block leading-[20px]">CBP</p>
      </div>
    </div>
  );
}

function TableItem25() {
  return (
    <div
      className="bg-[#ffffff] h-[68px] relative shrink-0 w-full"
      data-name="Table Item"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-2.5 relative w-full">
          <IconText15 />
        </div>
      </div>
    </div>
  );
}

function Column6() {
  return (
    <div
      className="box-border content-stretch flex flex-col items-start justify-start p-0 relative shrink-0 w-[93px]"
      data-name="column"
    >
      <HeaderItem6 />
      {[...Array(3).keys()].map((_, i) => (
        <TableItem25 key={i} />
      ))}
    </div>
  );
}

function TableItem28() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start p-0 relative shrink-0"
      data-name="Table Item"
    >
      <div className="font-['Urbane:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Review 1</p>
      </div>
    </div>
  );
}

function HeaderItem7() {
  return (
    <div className="relative shrink-0 w-full" data-name="header item">
      <div
        aria-hidden="true"
        className="absolute border-[#f1f5f7] border-[0px_0px_1px] border-solid inset-0 pointer-events-none"
      />
      <div className="relative size-full">
        <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start px-3 py-0 relative w-full">
          <TableItem28 />
        </div>
      </div>
    </div>
  );
}

function TableItem29() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-[13px] relative shrink-0"
      data-name="Table Item"
    >
      <div className="flex flex-col font-['Urbane:Light',_sans-serif] justify-end leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Jane Chu</p>
      </div>
    </div>
  );
}

function Column7() {
  return (
    <div
      className="box-border content-stretch flex flex-col items-start justify-start p-0 relative shrink-0 w-[140px]"
      data-name="column"
    >
      <HeaderItem7 />
      {[...Array(3).keys()].map((_, i) => (
        <TableItem29 key={i} />
      ))}
    </div>
  );
}

function TableItem32() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start p-0 relative shrink-0"
      data-name="Table Item"
    >
      <div className="font-['Urbane:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Review 2</p>
      </div>
    </div>
  );
}

function HeaderItem8() {
  return (
    <div className="relative shrink-0 w-full" data-name="header item">
      <div
        aria-hidden="true"
        className="absolute border-[#f1f5f7] border-[0px_0px_1px] border-solid inset-0 pointer-events-none"
      />
      <div className="relative size-full">
        <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start px-3 py-0 relative w-full">
          <TableItem32 />
        </div>
      </div>
    </div>
  );
}

function TableItem33() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-0 relative shrink-0 w-[110px]"
      data-name="Table Item"
    >
      <div className="flex flex-col font-['Urbane:Light',_sans-serif] justify-end leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">-</p>
      </div>
    </div>
  );
}

function Column8() {
  return (
    <div
      className="box-border content-stretch flex flex-col items-start justify-start p-0 relative shrink-0 w-[140px]"
      data-name="column"
    >
      <HeaderItem8 />
      {[...Array(3).keys()].map((_, i) => (
        <TableItem33 key={i} />
      ))}
    </div>
  );
}

function TableItem36() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start p-0 relative shrink-0"
      data-name="Table Item"
    >
      <div className="font-['Urbane:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Assigned</p>
      </div>
    </div>
  );
}

function HeaderItem9() {
  return (
    <div className="relative shrink-0 w-full" data-name="header item">
      <div
        aria-hidden="true"
        className="absolute border-[#f1f5f7] border-[0px_0px_1px] border-solid inset-0 pointer-events-none"
      />
      <div className="relative size-full">
        <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start px-3 py-0 relative w-full">
          <TableItem36 />
        </div>
      </div>
    </div>
  );
}

function TableItem37() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-0 relative shrink-0 w-[110px]"
      data-name="Table Item"
    >
      <div className="flex flex-col font-['Urbane:Light',_sans-serif] justify-end leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">04/15/25 1:30pm</p>
      </div>
    </div>
  );
}

function Column9() {
  return (
    <div
      className="box-border content-stretch flex flex-col items-start justify-start p-0 relative shrink-0 w-[170px]"
      data-name="column"
    >
      <HeaderItem9 />
      {[...Array(3).keys()].map((_, i) => (
        <TableItem37 key={i} />
      ))}
    </div>
  );
}

function TableItem40() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start p-0 relative shrink-0"
      data-name="Table Item"
    >
      <div className="font-['Urbane:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#384455] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Status</p>
      </div>
    </div>
  );
}

function HeaderItem10() {
  return (
    <div className="relative shrink-0 w-full" data-name="header item">
      <div
        aria-hidden="true"
        className="absolute border-[#f1f5f7] border-[0px_0px_1px] border-solid inset-0 pointer-events-none"
      />
      <div className="relative size-full">
        <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start px-3 py-0 relative w-full">
          <TableItem40 />
        </div>
      </div>
    </div>
  );
}

function ButtonBase1() {
  return (
    <div
      className="basis-0 bg-[#3870b8] grow min-h-px min-w-px relative rounded-lg shrink-0"
      data-name="_Button base"
    >
      <div className="flex flex-row items-center justify-center overflow-clip relative size-full">
        <div className="box-border content-stretch flex flex-row gap-2 items-center justify-center px-4 py-2.5 relative w-full">
          <div className="font-['Urbane:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#ffffff] text-[12px] text-left text-nowrap">
            <p className="block leading-[20px] whitespace-pre">Review</p>
          </div>
        </div>
      </div>
    </div>
  );
}

function SumbitButton() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-row grow items-start justify-start min-h-px min-w-px p-0 relative rounded-lg shrink-0"
      data-name="sumbit-button"
    >
      <ButtonBase1 />
    </div>
  );
}

function TableItem41() {
  return (
    <div className="h-[68px] relative shrink-0 w-full" data-name="Table Item">
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-0 relative w-full">
          <SumbitButton />
        </div>
      </div>
    </div>
  );
}

function ButtonBase2() {
  return (
    <div
      className="basis-0 bg-[#bfd0ee] grow min-h-px min-w-px relative rounded-lg shrink-0"
      data-name="_Button base"
    >
      <div className="flex flex-row items-center justify-center overflow-clip relative size-full">
        <div className="box-border content-stretch flex flex-row gap-2 items-center justify-center px-4 py-2.5 relative w-full">
          <div className="font-['Urbane:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#ffffff] text-[12px] text-left text-nowrap">
            <p className="block leading-[20px] whitespace-pre">Review</p>
          </div>
        </div>
      </div>
    </div>
  );
}

function SumbitButton1() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-row grow items-start justify-start min-h-px min-w-px p-0 relative rounded-lg shrink-0"
      data-name="sumbit-button"
    >
      <ButtonBase2 />
    </div>
  );
}

function TableItem42() {
  return (
    <div className="h-[68px] relative shrink-0 w-full" data-name="Table Item">
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-0 relative w-full">
          <SumbitButton1 />
        </div>
      </div>
    </div>
  );
}

function Column10() {
  return (
    <div
      className="box-border content-stretch flex flex-col items-start justify-start p-0 relative shrink-0 w-[118px]"
      data-name="column"
    >
      <HeaderItem10 />
      <TableItem41 />
      {[...Array(2).keys()].map((_, i) => (
        <TableItem42 key={i} />
      ))}
    </div>
  );
}

function Columns() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-row grow items-start justify-start min-h-px min-w-px p-0 relative shrink-0"
      data-name="columns"
    >
      <Column />
      <Column1 />
      <Column2 />
      <Column3 />
      <Column4 />
      <Column5 />
      <Column6 />
      <Column7 />
      <Column8 />
      <Column9 />
      <Column10 />
    </div>
  );
}

function Table() {
  return (
    <div
      className="box-border content-stretch flex flex-row items-start justify-start p-0 relative shrink-0 w-full"
      data-name="table"
    >
      <Columns />
    </div>
  );
}

function Table1() {
  return (
    <div
      className="bg-[#ffffff] relative rounded-lg shrink-0 w-full"
      data-name="Table"
    >
      <div
        aria-hidden="true"
        className="absolute border border-[#f1f5f7] border-solid inset-0 pointer-events-none rounded-lg"
      />
      <div className="flex flex-col justify-center relative size-full">
        <div className="box-border content-stretch flex flex-col items-start justify-center p-[20px] relative w-full">
          <Head />
          <Table />
        </div>
      </div>
    </div>
  );
}

function AssignedTable() {
  return (
    <div
      className="box-border content-stretch flex flex-col gap-5 items-start justify-start p-0 relative shrink-0 w-[1380px]"
      data-name="assigned-table"
    >
      <Table1 />
    </div>
  );
}

function Content() {
  return (
    <div className="relative shrink-0 w-full" data-name="content">
      <div className="relative size-full">
        <div className="box-border content-stretch flex flex-col gap-5 items-start justify-start px-[30px] py-0 relative w-full">
          <div className="font-['Urbane:Demi_Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[24px] text-left text-nowrap">
            <p className="block leading-[32px] whitespace-pre">Dashboard</p>
          </div>
          <AssignedTable />
        </div>
      </div>
    </div>
  );
}

function Content1() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-col gap-5 grow items-start justify-start min-h-px min-w-px px-0 py-5 relative shrink-0 w-full"
      data-name="content"
    >
      <Content />
    </div>
  );
}

function Screen() {
  return (
    <div
      className="bg-[#f9fbfc] box-border content-stretch flex flex-col h-[900px] items-start justify-start p-0 relative shrink-0 w-[1440px]"
      data-name="screen"
    >
      <Menu1 />
      <Content1 />
    </div>
  );
}

export default function Dashboard() {
  return (
    <div
      className="bg-[#f6f6f6] box-border content-stretch flex flex-col items-start justify-start p-0 relative size-full"
      data-name="Dashboard"
    >
      <Screen />
    </div>
  );
}