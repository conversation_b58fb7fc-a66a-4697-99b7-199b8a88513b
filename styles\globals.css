@import url('https://fonts.googleapis.com/css2?family=Urbane:wght@300;400;500;600;700&display=swap');

@custom-variant dark (&:is(.dark *));

:root {
  --font-size: 14px;
  /* Typography variables */
  --text-h1: 20px;
  --text-h2: 14px;
  --text-h3: 12px;
  --text-h4: 12px;
  --text-base: 12px;
  --text-label: 10px;
  --font-weight-demi-bold: 600;
  --font-weight-medium: 500;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-family-urbane: 'Urbane', sans-serif;
  
  /* The default background color for the application. */
  --background: rgba(249, 251, 252, 1.00);
  /* The default color for elements or text that appears on top of the background. */
  --foreground: rgba(23, 24, 26, 1.00);
  /* The background color for cards, modals, and other containers. */
  --card: rgba(255, 255, 255, 1.00);
  /* The default color for text or elements that appear on top of cards, modals and other background containers. */
  --card-foreground: rgba(23, 24, 26, 1.00);
  /* The background color for dropdowns and tooltips. */
  --popover: rgba(255, 255, 255, 1.00);
  /* The default color for text or elements that appear on top of dropdowns and tooltips. */
  --popover-foreground: rgba(23, 24, 26, 1.00);
  /* The primary color used for buttons, links, and other interactive elements. */
  --primary: rgba(56, 112, 184, 1.00);
  /* The default color for text or elements that appear on top of primary colored elements. */
  --primary-foreground: rgba(255, 255, 255, 1.00);
  /* The secondary color used for secondary(but NOT-DESTRUCTIVE) buttons and interactive elements. */
  --secondary: rgba(241, 245, 247, 1.00);
  /* The default color for text or elements that appear on top of secondary colored elements. */
  --secondary-foreground: rgba(23, 24, 26, 1.00);
  /* The muted color used for less prominent elements, such as disabled buttons or disabled text. */
  --muted: rgba(191, 208, 238, 0.25);
  /* The default color for text or elements that appear on top of muted colored elements. */
  --muted-foreground: rgba(84, 121, 150, 1.00);
  /* The accent color used for highlights, links, and other interactive elements. */
  --accent: rgba(0, 113, 188, 1.00);
  /* The default color for text or elements that appear on top of accent colored elements. */
  --accent-foreground: rgba(255, 255, 255, 1.00);
  /* The color used for background in destructive actions, such as delete buttons or error messages. */
  --destructive: rgba(220, 38, 127, 1.00);
  /* The default color for text or elements that appear on top of destructive colored elements. */
  --destructive-foreground: rgba(255, 255, 255, 1.00);
  /* The default border color for elements such as inputs, buttons, and other containers. */
  --border: rgba(217, 225, 231, 1.00);
  /* The default background color for input fields once the text has been filled. Should be either transparent or match the input-background. */
  --input: rgba(255, 255, 255, 1.00);
  /* The default background color for input fields, text areas, and other input elements. */
  --input-background: rgba(255, 255, 255, 1.00);
  /* The color for focus rings, outlines, and other focus indicators. */
  --ring: rgba(84, 121, 150, 1.00);
  /* Shadow for small elevations, such as cards or modals. */
  --elevation-sm: 0px 1px 3px 0px rgba(0, 0, 0, 0.1);
  /* The color for chart elements, such as a bar or line in a chart. */
  --chart-1: rgba(56, 112, 184, 1.00);
  /* The color for another chart element, such as a bar or line in a chart. */
  --chart-2: rgba(128, 159, 184, 1.00);
  /* The color for another chart element, such as a bar or line in a chart. */
  --chart-3: rgba(84, 121, 150, 1.00);
  /* The color for another chart element, such as a bar or line in a chart. */
  --chart-4: rgba(0, 113, 188, 1.00);
  /* The color for another chart element, such as a bar or line in a chart. */
  --chart-5: rgba(23, 24, 26, 1.00);
  /* The default border radius for elements such as buttons, cards, and other containers. */
  --radius: 8px;
  /* The border radius for buttons. */
  --radius-button: 8px;
  /* The border radius for input fields. */
  --radius-input: 10px;
  /* The border radius for checkboxes. */
  --radius-checkbox: 5px;
  /* The background color for sidebars, navigation menus, and other persistent elements. */
  --sidebar: rgba(255, 255, 255, 1.00);
  /* The default color for text or elements that appear on top of sidebars, navigation menus, and other persistent elements. */
  --sidebar-foreground: rgba(23, 24, 26, 1.00);
  /* The primary color used for buttons, links, and other interactive elements in sidebars and navigation menus. */
  --sidebar-primary: rgba(56, 112, 184, 1.00);
  /* The default color for text or elements that appear on top of primary colored elements in sidebars and navigation menus. */
  --sidebar-primary-foreground: rgba(255, 255, 255, 1.00);
  /* The accent color used for highlights, links, and other interactive elements in sidebars and navigation menus. */
  --sidebar-accent: rgba(0, 113, 188, 1.00);
  /* The default color for text or elements that appear on top of accent colored elements in sidebars and navigation menus. */
  --sidebar-accent-foreground: rgba(255, 255, 255, 1.00);
  /* The default border color for elements in sidebars and navigation menus. */
  --sidebar-border: rgba(241, 245, 247, 1.00);
  /* The default color for focus rings, outlines, and other focus indicators in sidebars and navigation menus. */
  --sidebar-ring: rgba(84, 121, 150, 1.00);
}

.dark {
  --background: rgba(23, 24, 26, 1.00);
  --foreground: rgba(249, 251, 252, 1.00);
  --card: rgba(40, 42, 46, 1.00);
  --card-foreground: rgba(249, 251, 252, 1.00);
  --popover: rgba(40, 42, 46, 1.00);
  --popover-foreground: rgba(249, 251, 252, 1.00);
  --primary: rgba(84, 139, 214, 1.00);
  --primary-foreground: rgba(255, 255, 255, 1.00);
  --secondary: rgba(60, 64, 70, 1.00);
  --secondary-foreground: rgba(249, 251, 252, 1.00);
  --muted: rgba(60, 64, 70, 1.00);
  --muted-foreground: rgba(156, 163, 175, 1.00);
  --accent: rgba(34, 139, 214, 1.00);
  --accent-foreground: rgba(255, 255, 255, 1.00);
  --destructive: rgba(239, 68, 68, 1.00);
  --destructive-foreground: rgba(255, 255, 255, 1.00);
  --border: rgba(75, 85, 99, 1.00);
  --input: rgba(40, 42, 46, 1.00);
  --input-background: rgba(40, 42, 46, 1.00);
  --ring: rgba(156, 163, 175, 1.00);
  --elevation-sm: 0px 1px 3px 0px rgba(0, 0, 0, 0.3);
  --chart-1: rgba(84, 139, 214, 1.00);
  --chart-2: rgba(156, 180, 214, 1.00);
  --chart-3: rgba(156, 163, 175, 1.00);
  --chart-4: rgba(34, 139, 214, 1.00);
  --chart-5: rgba(249, 251, 252, 1.00);
  --sidebar: rgba(40, 42, 46, 1.00);
  --sidebar-foreground: rgba(249, 251, 252, 1.00);
  --sidebar-primary: rgba(84, 139, 214, 1.00);
  --sidebar-primary-foreground: rgba(255, 255, 255, 1.00);
  --sidebar-accent: rgba(34, 139, 214, 1.00);
  --sidebar-accent-foreground: rgba(255, 255, 255, 1.00);
  --sidebar-border: rgba(75, 85, 99, 1.00);
  --sidebar-ring: rgba(156, 163, 175, 1.00);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-input-background: var(--input-background);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 3px);
  --radius-md: var(--radius);
  --radius-lg: var(--radius-button);
  --radius-xl: var(--radius-input);
  --radius-checkbox: var(--radius-checkbox);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
    font-family: var(--font-family-urbane);
  }
}

h1 {
  font-family: var(--font-family-urbane);
  font-size: var(--text-h1);
  font-weight: var(--font-weight-demi-bold);
  line-height: 1.6;
}

h2 {
  font-family: var(--font-family-urbane);
  font-size: var(--text-h2);
  font-weight: var(--font-weight-demi-bold);
  line-height: 1.43;
}

h3 {
  font-family: var(--font-family-urbane);
  font-size: var(--text-h3);
  font-weight: var(--font-weight-demi-bold);
  line-height: 1.67;
}

h4 {
  font-family: var(--font-family-urbane);
  font-size: var(--text-h4);
  font-weight: var(--font-weight-medium);
  line-height: 3.33;
}

p {
  font-family: var(--font-family-urbane);
  font-size: var(--text-base);
  font-weight: var(--font-weight-light);
  line-height: 1.33;
}

span {
  font-family: var(--font-family-urbane);
  font-size: var(--text-base);
  font-weight: var(--font-weight-light);
  line-height: 1.33;
}

label {
  font-family: var(--font-family-urbane);
  font-size: var(--text-label);
  font-weight: var(--font-weight-light);
  line-height: 1.2;
}

button {
  font-family: var(--font-family-urbane);
  font-size: var(--text-base);
  font-weight: var(--font-weight-medium);
  line-height: 1.67;
}

input {
  font-family: var(--font-family-urbane);
  font-size: var(--text-base);
  font-weight: var(--font-weight-light);
  line-height: 1.33;
}

html {
  font-size: var(--font-size);
}