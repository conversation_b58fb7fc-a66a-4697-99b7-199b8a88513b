import svgPaths from "./svg-e14qgdqkb1";

function IconStroke() {
  return (
    <div className="h-2 relative shrink-0 w-3.5" data-name="icon_stroke">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 14 8"
      >
        <g id="icon_stroke">
          <path
            d={svgPaths.p37ae0a00}
            id="Vector"
            stroke="var(--stroke-0, #17181A)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
        </g>
      </svg>
    </div>
  );
}

function Row() {
  return (
    <div
      className="bg-[#ffffff] order-[14] relative rounded-md shrink-0 w-full"
      data-name="row"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 items-center justify-start px-3 py-2 relative w-full">
          <div className="basis-0 font-['Urbane:Light',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[#17181a] text-[12px] text-left">
            <p className="block leading-[20px]">Select</p>
          </div>
          <IconStroke />
        </div>
      </div>
    </div>
  );
}

function Div() {
  return (
    <div
      className="bg-[#ffffff] h-2 order-[13] relative rounded-md shrink-0 w-full"
      data-name="div"
    >
      <div className="absolute bg-[#547996] h-px left-[-8px] right-[-8px] top-1/2 translate-y-[-50%]" />
    </div>
  );
}

function Check() {
  return (
    <div className="relative shrink-0 size-4" data-name="check">
      <div className="absolute bg-[#ffffff] left-0 rounded-[5px] size-4 top-0">
        <div
          aria-hidden="true"
          className="absolute border border-[#d9e1e7] border-solid inset-0 pointer-events-none rounded-[5px]"
        />
      </div>
    </div>
  );
}

function Row1() {
  return (
    <div
      className="bg-[#ffffff] order-12 relative rounded-md shrink-0 w-full"
      data-name="row"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 items-center justify-start px-3 py-2 relative w-full">
          <Check />
          <div className="basis-0 font-['Urbane:Light',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[#17181a] text-[12px] text-left">
            <p className="block leading-[20px]">
              Blood pressure values do not match
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

function Check1() {
  return (
    <div className="relative shrink-0 size-4" data-name="check">
      <div className="absolute bg-[#ffffff] left-0 rounded-[5px] size-4 top-0">
        <div
          aria-hidden="true"
          className="absolute border border-[#d9e1e7] border-solid inset-0 pointer-events-none rounded-[5px]"
        />
      </div>
    </div>
  );
}

function Row2() {
  return (
    <button
      className="bg-[#ffffff] cursor-pointer order-11 relative rounded-md shrink-0 w-full"
      data-name="row"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 items-center justify-start px-3 py-2 relative w-full">
          <Check1 />
          <div className="basis-0 font-['Urbane:Light',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[#17181a] text-[12px] text-left">
            <p className="block leading-[20px]">BMI not found</p>
          </div>
        </div>
      </div>
    </button>
  );
}

function Check2() {
  return (
    <div className="relative shrink-0 size-4" data-name="check">
      <div className="absolute bg-[#ffffff] left-0 rounded-[5px] size-4 top-0">
        <div
          aria-hidden="true"
          className="absolute border border-[#d9e1e7] border-solid inset-0 pointer-events-none rounded-[5px]"
        />
      </div>
    </div>
  );
}

function Row3() {
  return (
    <div
      className="bg-[#ffffff] order-10 relative rounded-md shrink-0 w-full"
      data-name="row"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 items-center justify-start px-3 py-2 relative w-full">
          <Check2 />
          <div className="basis-0 font-['Urbane:Light',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[#17181a] text-[12px] text-left">
            <p className="block leading-[20px]">Chart summary not found</p>
          </div>
        </div>
      </div>
    </div>
  );
}

function Check3() {
  return (
    <div className="relative shrink-0 size-4" data-name="check">
      <div className="absolute bg-[#ffffff] left-0 rounded-[5px] size-4 top-0">
        <div
          aria-hidden="true"
          className="absolute border border-[#d9e1e7] border-solid inset-0 pointer-events-none rounded-[5px]"
        />
      </div>
    </div>
  );
}

function Row4() {
  return (
    <div
      className="bg-[#ffffff] order-9 relative rounded-md shrink-0 w-full"
      data-name="row"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 items-center justify-start px-3 py-2 relative w-full">
          <Check3 />
          <div className="basis-0 font-['Urbane:Light',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[#17181a] text-[12px] text-left">
            <p className="block leading-[20px]">Dates do not match</p>
          </div>
        </div>
      </div>
    </div>
  );
}

function Check4() {
  return (
    <div className="relative shrink-0 size-4" data-name="check">
      <div className="absolute bg-[#ffffff] left-0 rounded-[5px] size-4 top-0">
        <div
          aria-hidden="true"
          className="absolute border border-[#d9e1e7] border-solid inset-0 pointer-events-none rounded-[5px]"
        />
      </div>
    </div>
  );
}

function Row5() {
  return (
    <div
      className="bg-[#ffffff] order-8 relative rounded-md shrink-0 w-full"
      data-name="row"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 items-center justify-start px-3 py-2 relative w-full">
          <Check4 />
          <div className="basis-0 font-['Urbane:Light',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[#17181a] text-[12px] text-left">
            <p className="block leading-[20px]">
              Documentation does not match entry
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

function Check5() {
  return (
    <div className="relative shrink-0 size-4" data-name="check">
      <div className="absolute bg-[#ffffff] left-0 rounded-[5px] size-4 top-0">
        <div
          aria-hidden="true"
          className="absolute border border-[#d9e1e7] border-solid inset-0 pointer-events-none rounded-[5px]"
        />
      </div>
    </div>
  );
}

function Row6() {
  return (
    <div
      className="bg-[#ffffff] order-7 relative rounded-md shrink-0 w-full"
      data-name="row"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 items-center justify-start px-3 py-2 relative w-full">
          <Check5 />
          <div className="basis-0 font-['Urbane:Light',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[#17181a] text-[12px] text-left">
            <p className="block leading-[20px]">Insufficient documentation</p>
          </div>
        </div>
      </div>
    </div>
  );
}

function Check6() {
  return (
    <div className="relative shrink-0 size-4" data-name="check">
      <div className="absolute bg-[#ffffff] left-0 rounded-[5px] size-4 top-0">
        <div
          aria-hidden="true"
          className="absolute border border-[#d9e1e7] border-solid inset-0 pointer-events-none rounded-[5px]"
        />
      </div>
    </div>
  );
}

function Row7() {
  return (
    <div
      className="bg-[#ffffff] order-6 relative rounded-md shrink-0 w-full"
      data-name="row"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 items-center justify-start px-3 py-2 relative w-full">
          <Check6 />
          <div className="basis-0 font-['Urbane:Light',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[#17181a] text-[12px] text-left">
            <p className="block leading-[20px]">
              Insufficient patient identifiers
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

function Check7() {
  return (
    <div className="relative shrink-0 size-4" data-name="check">
      <div className="absolute bg-[#ffffff] left-0 rounded-[5px] size-4 top-0">
        <div
          aria-hidden="true"
          className="absolute border border-[#d9e1e7] border-solid inset-0 pointer-events-none rounded-[5px]"
        />
      </div>
    </div>
  );
}

function Row8() {
  return (
    <div
      className="bg-[#ffffff] order-5 relative rounded-md shrink-0 w-full"
      data-name="row"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 items-center justify-start px-3 py-2 relative w-full">
          <Check7 />
          <div className="basis-0 font-['Urbane:Light',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[#17181a] text-[12px] text-left">
            <p className="block leading-[20px]">Lab value not entered</p>
          </div>
        </div>
      </div>
    </div>
  );
}

function Check8() {
  return (
    <div className="relative shrink-0 size-4" data-name="check">
      <div className="absolute bg-[#ffffff] left-0 rounded-[5px] size-4 top-0">
        <div
          aria-hidden="true"
          className="absolute border border-[#d9e1e7] border-solid inset-0 pointer-events-none rounded-[5px]"
        />
      </div>
    </div>
  );
}

function Row9() {
  return (
    <div
      className="bg-[#ffffff] order-4 relative rounded-md shrink-0 w-full"
      data-name="row"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 items-center justify-start px-3 py-2 relative w-full">
          <Check8 />
          <div className="basis-0 font-['Urbane:Light',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[#17181a] text-[12px] text-left">
            <p className="block leading-[20px]">No documentation</p>
          </div>
        </div>
      </div>
    </div>
  );
}

function Check9() {
  return (
    <div className="relative shrink-0 size-4" data-name="check">
      <div className="absolute bg-[#ffffff] left-0 rounded-[5px] size-4 top-0">
        <div
          aria-hidden="true"
          className="absolute border border-[#d9e1e7] border-solid inset-0 pointer-events-none rounded-[5px]"
        />
      </div>
    </div>
  );
}

function Row10() {
  return (
    <div
      className="bg-[#ffffff] order-3 relative rounded-md shrink-0 w-full"
      data-name="row"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 items-center justify-start px-3 py-2 relative w-full">
          <Check9 />
          <div className="basis-0 font-['Urbane:Light',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[#17181a] text-[12px] text-left">
            <p className="block leading-[20px]">
              Out of timeframe for measurement period
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

function Check10() {
  return (
    <div className="relative shrink-0 size-4" data-name="check">
      <div className="absolute bg-[#ffffff] left-0 rounded-[5px] size-4 top-0">
        <div
          aria-hidden="true"
          className="absolute border border-[#d9e1e7] border-solid inset-0 pointer-events-none rounded-[5px]"
        />
      </div>
    </div>
  );
}

function Row11() {
  return (
    <div
      className="bg-[#ffffff] order-2 relative rounded-md shrink-0 w-full"
      data-name="row"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 items-center justify-start px-3 py-2 relative w-full">
          <Check10 />
          <div className="basis-0 font-['Urbane:Light',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[#17181a] text-[12px] text-left">
            <p className="block leading-[20px]">
              Patient name/DOB does not match records
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

function Check11() {
  return (
    <div className="relative shrink-0 size-4" data-name="check">
      <div className="absolute bg-[#ffffff] left-0 rounded-[5px] size-4 top-0">
        <div
          aria-hidden="true"
          className="absolute border border-[#d9e1e7] border-solid inset-0 pointer-events-none rounded-[5px]"
        />
      </div>
    </div>
  );
}

function Row12() {
  return (
    <div
      className="bg-[#ffffff] order-1 relative rounded-md shrink-0 w-full"
      data-name="row"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 items-center justify-start px-3 py-2 relative w-full">
          <Check11 />
          <div className="basis-0 font-['Urbane:Light',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[#17181a] text-[12px] text-left">
            <p className="block leading-[20px]">Other</p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function DropdownNone() {
  return (
    <div
      className="bg-[#ffffff] relative rounded-[10px] size-full"
      data-name="Dropdown_none"
    >
      <div className="flex flex-col items-center justify-center relative size-full">
        <div className="box-border content-stretch flex flex-col-reverse items-center justify-center overflow-clip p-[4px] relative size-full">
          <Row />
          <Div />
          <Row1 />
          <Row2 />
          <Row3 />
          <Row4 />
          <Row5 />
          <Row6 />
          <Row7 />
          <Row8 />
          <Row9 />
          <Row10 />
          <Row11 />
          <Row12 />
        </div>
      </div>
      <div
        aria-hidden="true"
        className="absolute border border-[#547996] border-solid inset-0 pointer-events-none rounded-[10px]"
      />
    </div>
  );
}