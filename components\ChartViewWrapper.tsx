import { useState, useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IconArrowDown, IconArrow } from "./shared/Icons";
import { MedicalChartPage } from "./MedicalChartPage";
import { SubmissionData } from "../App";
import { <PERSON><PERSON> } from "./ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "./ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Input } from "./ui/input";
import { Textarea } from "./ui/textarea";
import { Checkbox } from "./ui/checkbox";
import { Label } from "./ui/label";

interface ChartViewWrapperProps {
  chartId: string;
  onBackToDashboard: () => void;
  onSubmissionComplete: (data: SubmissionData) => void;
}

// Mock patient data mapping based on chartId
const patientDataMap: Record<string, {
  memberId: string;
  name: string;
  dob: string;
  gender: string;
  lob: string;
  provider: string;
  npi: string;
}> = {
  '1': {
    memberId: '55820474',
    name: '<PERSON>',
    dob: '01/05/1972',
    gender: 'Male',
    lob: 'MA HMO',
    provider: 'Dr. <PERSON> <PERSON>',
    npi: '**********'
  },
  '2': {
    memberId: '302274401',
    name: 'Alma Anders',
    dob: '12/15/1953',
    gender: 'Female',
    lob: 'MA HMO',
    provider: 'Dr. <PERSON> Roberts',
    npi: '**********'
  },
  '3': {
    memberId: '**********',
    name: 'Joanne Smith',
    dob: '06/30/1951',
    gender: 'Female',
    lob: 'MA HMO',
    provider: 'Dr. Lisa Chen',
    npi: '**********'
  }
};

interface AIHighlight {
  measure: string;
  dos: string;
  systolic: string;
  diastolic: string;
  page: number;
  selected: boolean;
}

interface ManualHighlight {
  id: string;
  pageNumber: number;
  lineIndex: number;
  startIndex: number;
  endIndex: number;
  text: string;
  timestamp: number;
}

interface FormResults {
  activeTab: 'inclusions' | 'exclusions' | 'none-found';
  telehealth: boolean;
  systolic: string;
  diastolic: string;
  dateOfService: string;
  notes: string;
  exclusionReasons: string[];
  exclusionDateOfService: string;
  noneFoundReasons: string[];
}

// Dropdown options for exclusions and none found reasons
const exclusionOptions = [
  "Blood pressure reading outside acceptable range",
  "Measurement taken in non-clinical setting",
  "Insufficient documentation",
  "Patient on antihypertensive medication",
  "Reading taken during acute illness",
  "Equipment malfunction documented",
  "Patient refused measurement"
];

const noneFoundOptions = [
  "No blood pressure readings documented",
  "All readings outside measurement period",
  "Documentation illegible or incomplete", 
  "Patient not seen during measurement period",
  "Records missing or unavailable",
  "Only home readings available",
  "Telehealth visit without vital signs"
];

export default function ChartViewWrapper({ chartId, onBackToDashboard, onSubmissionComplete }: ChartViewWrapperProps) {
  const pdfViewerRef = useRef<HTMLDivElement>(null);
  const [highlightMode, setHighlightMode] = useState(false);
  const [selectedHighlight, setSelectedHighlight] = useState<{ pageNumber: number; text: string } | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<Array<{ page: number; matches: number }>>([]);
  
  // Get patient info for this chart
  const patientInfo = patientDataMap[chartId] || patientDataMap['1']; // Fallback to chart 1

  // Mock AI highlights data
  const [aiHighlights, setAiHighlights] = useState<AIHighlight[]>([
    {
      measure: 'CBP',
      dos: '07/21/2024',
      systolic: '136',
      diastolic: '82',
      page: 2,
      selected: false
    },
    {
      measure: 'CBP',
      dos: '07/21/2024',
      systolic: '140',
      diastolic: '82',
      page: 2,
      selected: false
    },
    {
      measure: 'CBP',
      dos: '05/21/2024',
      systolic: '150',
      diastolic: '90',
      page: 7,
      selected: false
    }
  ]);

  // Manual highlights state
  const [manualHighlights, setManualHighlights] = useState<ManualHighlight[]>([]);

  // Form results state
  const [formResults, setFormResults] = useState<FormResults>({
    activeTab: 'inclusions',
    telehealth: false,
    systolic: '',
    diastolic: '',
    dateOfService: '',
    notes: '',
    exclusionReasons: [],
    exclusionDateOfService: '',
    noneFoundReasons: []
  });

  const scrollToPage = (pageNumber: number) => {
    if (pdfViewerRef.current) {
      const pageElement = pdfViewerRef.current.querySelector(`[data-page="${pageNumber}"]`);
      if (pageElement) {
        pageElement.scrollIntoView({ 
          behavior: 'smooth',
          block: 'start'
        });
      }
    }
  };

  const toggleHighlightSelection = (index: number) => {
    setAiHighlights(prev => prev.map((item, i) => 
      i === index ? { ...item, selected: !item.selected } : item
    ));
    
    // Auto-populate form when highlighting gets selected
    const highlight = aiHighlights[index];
    if (highlight && !highlight.selected) {
      setFormResults(prev => ({
        ...prev,
        systolic: highlight.systolic,
        diastolic: highlight.diastolic,
        dateOfService: highlight.dos
      }));
    }
  };

  const handleSubmit = () => {
    const submissionData: SubmissionData = {
      chartId,
      patientInfo,
      aiHighlights,
      formResults,
      manualHighlights,
      submittedAt: new Date().toISOString(),
      submittedBy: 'Jane Chu'
    };

    onSubmissionComplete(submissionData);
  };

  const toggleHighlightMode = () => {
    setHighlightMode(!highlightMode);
  };

  const handleTextHighlight = (pageNumber: number, text: string) => {
    if (!highlightMode) return;

    const newHighlight: ManualHighlight = {
      id: Date.now().toString(),
      pageNumber,
      lineIndex: 0, // This would be calculated based on actual text position
      startIndex: 0,
      endIndex: text.length,
      text,
      timestamp: Date.now()
    };

    setManualHighlights(prev => [...prev, newHighlight]);
    setSelectedHighlight({ pageNumber, text });
  };

  const removeManualHighlight = (id: string) => {
    setManualHighlights(prev => prev.filter(h => h.id !== id));
  };

  const handleSearch = () => {
    if (!searchTerm.trim()) {
      setSearchResults([]);
      return;
    }

    // Mock search implementation - in a real app this would search through actual PDF content
    const mockResults = [];
    const searchTermLower = searchTerm.toLowerCase();
    
    // Simple mock: if search term contains common medical terms, show results
    const medicalTerms = ['blood', 'pressure', 'hypertension', 'medication', 'patient', 'history', 'diagnosis', 'treatment'];
    const hasMatch = medicalTerms.some(term => searchTermLower.includes(term) || term.includes(searchTermLower));
    
    if (hasMatch) {
      // Mock results showing matches on different pages
      mockResults.push(
        { page: 2, matches: 1 },
        { page: 4, matches: 2 },
        { page: 6, matches: 1 }
      );
    }
    
    setSearchResults(mockResults);
  };

  const clearSearch = () => {
    setSearchTerm('');
    setSearchResults([]);
  };

  const handleExclusionReasonToggle = (reason: string, checked: boolean) => {
    setFormResults(prev => ({
      ...prev,
      exclusionReasons: checked 
        ? [...prev.exclusionReasons, reason]
        : prev.exclusionReasons.filter(r => r !== reason)
    }));
  };

  const handleNoneFoundReasonToggle = (reason: string, checked: boolean) => {
    setFormResults(prev => ({
      ...prev,
      noneFoundReasons: checked 
        ? [...prev.noneFoundReasons, reason]
        : prev.noneFoundReasons.filter(r => r !== reason)
    }));
  };

  return (
    <div className="bg-background box-border content-stretch flex flex-col items-start justify-start p-0 relative size-full min-h-screen">
      <div className="bg-background box-border content-stretch flex flex-col min-h-screen items-start justify-start p-0 relative shrink-0 w-full">
        {/* Top Navigation */}
        <div className="bg-card box-border content-stretch flex flex-col h-16 sm:h-20 items-start justify-center p-0 relative shrink-0 w-full">
          <div aria-hidden="true" className="absolute border-b border-border inset-0 pointer-events-none" />
          <div className="basis-0 box-border content-stretch flex flex-row grow items-center justify-start min-h-px min-w-px p-0 relative shrink-0 w-full">
            <div className="basis-0 grow h-full min-h-px min-w-px relative shrink-0">
              <div className="flex flex-row items-center relative size-full">
                <div className="bg-clip-padding border-0 border-[transparent] border-solid box-border content-stretch flex flex-row items-center justify-start px-4 sm:px-[30px] py-3 relative size-full">
                  <div className="basis-0 box-border content-stretch flex flex-row gap-3 sm:gap-5 grow h-[46px] items-center justify-between min-h-px min-w-px p-0 relative shrink-0">
                    <div className="basis-0 box-border content-stretch flex flex-row gap-3 sm:gap-5 grow items-center justify-start min-h-px min-w-px p-0 relative shrink-0">
                      <Logo />
                    </div>
                    <div className="box-border content-stretch flex flex-row gap-2 sm:gap-4 items-center justify-end p-0 relative shrink-0">
                      <div className="box-border content-stretch flex flex-row gap-3 sm:gap-5 items-center justify-start px-2 sm:px-5 py-3 relative shrink-0">
                        <div className="box-border content-stretch flex flex-row gap-2 sm:gap-3 items-center justify-start p-0 relative shrink-0">
                          <p className="text-foreground hidden sm:block">Jane Chu</p>
                          <div className="bg-primary/20 box-border content-stretch flex flex-row gap-2 items-center justify-center overflow-clip p-[8px] relative rounded-[120px] shrink-0">
                            <IconUser />
                          </div>
                          <IconArrowDown />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="basis-0 box-border content-stretch flex flex-col gap-4 sm:gap-5 grow items-start justify-start min-h-px min-w-px px-0 py-4 sm:py-5 relative shrink-0 w-full">
          <div className="relative shrink-0 w-full">
            <div className="relative size-full">
              <div className="box-border content-stretch flex flex-col gap-4 sm:gap-5 items-start justify-start px-4 sm:px-[30px] py-0 relative w-full">
                
                {/* Header */}
                <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between w-full">
                  <div className="flex flex-col gap-2">
                    <h1 className="text-foreground">Chart Review</h1>
                    <p className="text-muted-foreground">Review patient medical chart and enter findings</p>
                  </div>
                  <button
                    onClick={onBackToDashboard}
                    className="box-border content-stretch flex flex-row gap-2 items-center justify-start p-2 relative shrink-0 hover:opacity-80 transition-opacity cursor-pointer touch-target"
                  >
                    <IconArrow />
                    <span className="text-accent">Back to Dashboard</span>
                  </button>
                </div>

                {/* Patient Information */}
                <div className="bg-card relative rounded-lg shrink-0 w-full shadow-[var(--elevation-sm)]">
                  <div aria-hidden="true" className="absolute border border-border inset-0 pointer-events-none rounded-lg" />
                  <div className="flex flex-col justify-center relative size-full">
                    <div className="box-border content-stretch flex flex-col items-start justify-center p-4 sm:p-[20px] relative w-full">
                      <h2 className="text-foreground mb-4">Patient Information</h2>
                      <div className="grid grid-cols-6 gap-4 w-full">
                        <div>
                          <span className="text-muted-foreground">Member ID:</span>
                          <p className="text-foreground font-[var(--font-weight-medium)]">{patientInfo.memberId}</p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Name:</span>
                          <p className="text-foreground font-[var(--font-weight-medium)]">{patientInfo.name}</p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">DOB:</span>
                          <p className="text-foreground font-[var(--font-weight-medium)]">{patientInfo.dob}</p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">LOB:</span>
                          <p className="text-foreground font-[var(--font-weight-medium)]">{patientInfo.lob}</p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Provider:</span>
                          <p className="text-foreground font-[var(--font-weight-medium)]">{patientInfo.provider}</p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">NPI:</span>
                          <p className="text-foreground font-[var(--font-weight-medium)]">{patientInfo.npi}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Chart and Form Layout - Updated proportions */}
                <div className="flex flex-col xl:flex-row gap-4 xl:gap-5 w-full">
                  
                  {/* Left Column - PDF Chart (65% width) */}
                  <div className="bg-card box-border content-stretch flex flex-col h-[600px] sm:h-[800px] xl:h-[1018px] items-start justify-start p-4 sm:p-[20px] relative rounded-lg shrink-0 w-full xl:w-[65%] xl:max-w-[65%] shadow-[var(--elevation-sm)]">
                    <div aria-hidden="true" className="absolute border border-border inset-0 pointer-events-none rounded-lg" />
                    
                    {/* Chart Header with Search and Controls */}
                    <div className="bg-card border-b border-border flex flex-col gap-3 p-3 w-full mb-4 rounded-t-lg">
                      <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center justify-between">
                        <h2 className="text-foreground font-[var(--font-weight-medium)]">Medical Chart</h2>
                        <div className="flex flex-row gap-2">
                          <button
                            onClick={toggleHighlightMode}
                            className={`px-3 py-2 rounded-lg transition-colors text-xs font-[var(--font-weight-medium)] ${
                              highlightMode 
                                ? 'bg-yellow-400 text-black hover:bg-yellow-500' 
                                : 'bg-secondary text-foreground hover:bg-secondary/80'
                            }`}
                          >
                            {highlightMode ? '🔆 Highlighting ON' : '🔆 Highlight'}
                          </button>
                        </div>
                      </div>
                      
                      {/* Search Section */}
                      <div className="flex flex-col sm:flex-row gap-2 w-full">
                        <div className="flex flex-row gap-2 flex-1">
                          <Input
                            type="text"
                            placeholder="Search for terminology..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                            className="flex-1 text-xs"
                          />
                          <Button
                            onClick={handleSearch}
                            className="px-4 py-2 text-xs font-[var(--font-weight-medium)]"
                          >
                            Search
                          </Button>
                          {searchTerm && (
                            <Button
                              variant="secondary"
                              onClick={clearSearch}
                              className="px-3 py-2 text-xs"
                            >
                              Clear
                            </Button>
                          )}
                        </div>
                      </div>
                      
                      {/* Search Results */}
                      {searchResults.length > 0 && (
                        <div className="flex flex-wrap gap-2">
                          <span className="text-xs text-muted-foreground">Found on pages:</span>
                          {searchResults.map((result, index) => (
                            <Button
                              key={index}
                              variant="ghost"
                              size="sm"
                              onClick={() => scrollToPage(result.page)}
                              className="px-2 py-1 text-xs h-auto"
                            >
                              Page {result.page} ({result.matches} match{result.matches !== 1 ? 'es' : ''})
                            </Button>
                          ))}
                        </div>
                      )}
                      
                      {/* Legend */}
                      <div className="flex flex-wrap gap-4 text-xs text-muted-foreground">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-blue-100 border border-blue-300 rounded"></div>
                          <span>AI Highlights (reference only)</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-yellow-200 border border-yellow-400 rounded"></div>
                          <span>Manual Highlights</span>
                        </div>
                      </div>
                    </div>
                    
                    <div
                      ref={pdfViewerRef}
                      className="bg-[#f5f5f5] flex-1 overflow-y-auto overflow-x-hidden relative shrink-0 w-full rounded-md"
                    >
                      {Array.from({ length: 8 }, (_, i) => i + 1).map(pageNumber => (
                        <MedicalChartPage 
                          key={pageNumber}
                          pageNumber={pageNumber}
                          manualHighlights={manualHighlights}
                          aiHighlights={aiHighlights}
                          highlightMode={highlightMode}
                          onTextHighlight={handleTextHighlight}
                          searchTerm={searchTerm}
                        />
                      ))}
                    </div>
                  </div>

                  {/* Right Column - Review Forms (35% width) */}
                  <div className="flex flex-col gap-4 xl:gap-5 w-full xl:w-[35%] xl:max-w-[35%]">
                    
                    {/* AI Highlights */}
                    <div className="bg-card relative rounded-lg shrink-0 w-full shadow-[var(--elevation-sm)]">
                      <div aria-hidden="true" className="absolute border border-border inset-0 pointer-events-none rounded-lg" />
                      <div className="flex flex-col justify-center relative size-full">
                        <div className="box-border content-stretch flex flex-col items-start justify-center p-4 sm:p-[20px] relative w-full">
                          <h2 className="text-foreground mb-4">AI Highlights</h2>
                          
                          {/* AI Highlights Table */}
                          <div className="w-full overflow-x-auto">
                            <div className="border-b border-border pb-2 mb-4 min-w-[300px]">
                              <div className="grid grid-cols-6 gap-1 text-xs">
                                <span className="text-muted-foreground font-[var(--font-weight-medium)]">Measure</span>
                                <span className="text-muted-foreground font-[var(--font-weight-medium)]">DoS</span>
                                <span className="text-muted-foreground font-[var(--font-weight-medium)]">Systolic</span>
                                <span className="text-muted-foreground font-[var(--font-weight-medium)]">Diastolic</span>
                                <span className="text-muted-foreground font-[var(--font-weight-medium)]">Page</span>
                                <span className="text-muted-foreground font-[var(--font-weight-medium)]">Select</span>
                              </div>
                            </div>
                            <div className="space-y-2 min-w-[300px]">
                              {aiHighlights.map((item, index) => (
                                <div key={index} className={`grid grid-cols-6 gap-1 text-xs py-2 rounded-md transition-colors ${
                                  item.selected ? 'bg-blue-50 border border-blue-200' : 'hover:bg-secondary/30'
                                }`}>
                                  <span className="text-foreground truncate">{item.measure}</span>
                                  <span className="text-foreground truncate">{item.dos}</span>
                                  <span className="text-foreground truncate">{item.systolic}</span>
                                  <span className="text-foreground truncate">{item.diastolic}</span>
                                  <button 
                                    onClick={() => scrollToPage(item.page)}
                                    className="text-accent underline hover:text-accent/80 cursor-pointer transition-colors text-left touch-target truncate"
                                  >
                                    {item.page}
                                  </button>
                                  <div className="flex justify-center items-center">
                                    <Checkbox
                                      checked={item.selected}
                                      onCheckedChange={() => toggleHighlightSelection(index)}
                                      className="w-4 h-4"
                                    />
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Manual Highlights */}
                    {manualHighlights.length > 0 && (
                      <div className="bg-card relative rounded-lg shrink-0 w-full shadow-[var(--elevation-sm)]">
                        <div aria-hidden="true" className="absolute border border-border inset-0 pointer-events-none rounded-lg" />
                        <div className="flex flex-col justify-center relative size-full">
                          <div className="box-border content-stretch flex flex-col items-start justify-center p-4 sm:p-[20px] relative w-full">
                            <h2 className="text-foreground mb-4">Manual Highlights ({manualHighlights.length})</h2>
                            <div className="space-y-2 w-full max-h-48 overflow-y-auto">
                              {manualHighlights.map((highlight) => (
                                <div key={highlight.id} className="flex flex-row items-center justify-between text-xs bg-yellow-100 border border-yellow-200 rounded px-3 py-2">
                                  <div className="flex-1 truncate">
                                    <span className="text-foreground">{highlight.text}</span>
                                    <span className="text-muted-foreground ml-2">
                                      (Page{' '}
                                      <button 
                                        onClick={() => scrollToPage(highlight.pageNumber)}
                                        className="text-accent underline hover:text-accent/80 cursor-pointer transition-colors touch-target"
                                      >
                                        {highlight.pageNumber}
                                      </button>
                                      )
                                    </span>
                                  </div>
                                  <button
                                    onClick={() => removeManualHighlight(highlight.id)}
                                    className="ml-2 text-red-500 hover:text-red-700 transition-colors cursor-pointer touch-target"
                                  >
                                    ×
                                  </button>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Results Form with Tabs */}
                    <div className="bg-card relative rounded-lg shrink-0 w-full shadow-[var(--elevation-sm)]">
                      <div aria-hidden="true" className="absolute border border-border inset-0 pointer-events-none rounded-lg" />
                      <div className="flex flex-col justify-center relative size-full">
                        <div className="box-border content-stretch flex flex-col items-start justify-center p-4 sm:p-[20px] relative w-full">
                          <h2 className="text-foreground mb-4">Results</h2>
                          
                          <Tabs 
                            value={formResults.activeTab} 
                            onValueChange={(value) => setFormResults(prev => ({ ...prev, activeTab: value as 'inclusions' | 'exclusions' | 'none-found' }))}
                            className="w-full"
                          >
                            <TabsList className="grid w-full grid-cols-3 mb-4">
                              <TabsTrigger value="inclusions" className="text-xs">Inclusions</TabsTrigger>
                              <TabsTrigger value="exclusions" className="text-xs">Exclusions</TabsTrigger>
                              <TabsTrigger value="none-found" className="text-xs">None Found</TabsTrigger>
                            </TabsList>
                            
                            <TabsContent value="inclusions" className="space-y-4 mt-0">
                              <div className="space-y-3">
                                <div className="flex items-center space-x-2">
                                  <Checkbox 
                                    id="telehealth"
                                    checked={formResults.telehealth}
                                    onCheckedChange={(checked) => setFormResults(prev => ({ ...prev, telehealth: !!checked }))}
                                  />
                                  <Label htmlFor="telehealth" className="text-foreground">Telehealth</Label>
                                </div>
                                
                                <div className="grid grid-cols-2 gap-3">
                                  <div className="space-y-1">
                                    <Label className="text-muted-foreground">Systolic</Label>
                                    <Input 
                                      type="text"
                                      value={formResults.systolic}
                                      onChange={(e) => setFormResults(prev => ({ ...prev, systolic: e.target.value }))}
                                      className="text-xs"
                                      placeholder="120"
                                    />
                                  </div>
                                  <div className="space-y-1">
                                    <Label className="text-muted-foreground">Diastolic</Label>
                                    <Input 
                                      type="text"
                                      value={formResults.diastolic}
                                      onChange={(e) => setFormResults(prev => ({ ...prev, diastolic: e.target.value }))}
                                      className="text-xs"
                                      placeholder="80"
                                    />
                                  </div>
                                </div>
                                
                                <div className="space-y-1">
                                  <Label className="text-muted-foreground">Date of Service</Label>
                                  <Input 
                                    type="date"
                                    value={formResults.dateOfService}
                                    onChange={(e) => setFormResults(prev => ({ ...prev, dateOfService: e.target.value }))}
                                    className="text-xs"
                                  />
                                </div>
                                
                                <div className="space-y-1">
                                  <Label className="text-muted-foreground">Notes</Label>
                                  <Textarea
                                    value={formResults.notes}
                                    onChange={(e) => setFormResults(prev => ({ ...prev, notes: e.target.value }))}
                                    className="text-xs min-h-[80px] resize-none"
                                    placeholder="Enter your notes here..."
                                  />
                                </div>
                              </div>
                            </TabsContent>
                            
                            <TabsContent value="exclusions" className="space-y-4 mt-0">
                              <div className="space-y-3">
                                <div className="space-y-1">
                                  <Label className="text-muted-foreground">Exclusion Reasons</Label>
                                  <div className="space-y-2 max-h-32 overflow-y-auto border border-border rounded-lg p-3">
                                    {exclusionOptions.map((option) => (
                                      <div key={option} className="flex items-center space-x-2">
                                        <Checkbox 
                                          id={`exclusion-${option}`}
                                          checked={formResults.exclusionReasons.includes(option)}
                                          onCheckedChange={(checked) => handleExclusionReasonToggle(option, !!checked)}
                                        />
                                        <Label htmlFor={`exclusion-${option}`} className="text-xs text-foreground leading-tight">
                                          {option}
                                        </Label>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                                
                                <div className="space-y-1">
                                  <Label className="text-muted-foreground">Date of Service</Label>
                                  <Input 
                                    type="date"
                                    value={formResults.exclusionDateOfService}
                                    onChange={(e) => setFormResults(prev => ({ ...prev, exclusionDateOfService: e.target.value }))}
                                    className="text-xs"
                                  />
                                </div>
                                
                                <div className="space-y-1">
                                  <Label className="text-muted-foreground">Notes</Label>
                                  <Textarea
                                    value={formResults.notes}
                                    onChange={(e) => setFormResults(prev => ({ ...prev, notes: e.target.value }))}
                                    className="text-xs min-h-[80px] resize-none"
                                    placeholder="Enter your notes here..."
                                  />
                                </div>
                              </div>
                            </TabsContent>
                            
                            <TabsContent value="none-found" className="space-y-4 mt-0">
                              <div className="space-y-3">
                                <div className="space-y-1">
                                  <Label className="text-muted-foreground">None Found Reasons</Label>
                                  <div className="space-y-2 max-h-32 overflow-y-auto border border-border rounded-lg p-3">
                                    {noneFoundOptions.map((option) => (
                                      <div key={option} className="flex items-center space-x-2">
                                        <Checkbox 
                                          id={`none-found-${option}`}
                                          checked={formResults.noneFoundReasons.includes(option)}
                                          onCheckedChange={(checked) => handleNoneFoundReasonToggle(option, !!checked)}
                                        />
                                        <Label htmlFor={`none-found-${option}`} className="text-xs text-foreground leading-tight">
                                          {option}
                                        </Label>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                                
                                <div className="space-y-1">
                                  <Label className="text-muted-foreground">Notes</Label>
                                  <Textarea
                                    value={formResults.notes}
                                    onChange={(e) => setFormResults(prev => ({ ...prev, notes: e.target.value }))}
                                    className="text-xs min-h-[80px] resize-none"
                                    placeholder="Enter your notes here..."
                                  />
                                </div>
                              </div>
                            </TabsContent>
                          </Tabs>
                        </div>
                      </div>
                    </div>

                    {/* Submit Button */}
                    <div className="flex justify-end w-full">
                      <Button
                        onClick={handleSubmit}
                        className="px-6 py-3 w-full sm:w-auto"
                      >
                        Submit Review
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}