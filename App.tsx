import { useState } from "react";
import MedicalChartDashboard from "./components/MedicalChartDashboard";
import ChartViewWrapper from "./components/ChartViewWrapper";
import ManagerReview from "./components/ManagerReview";

type View = 'dashboard' | 'chart' | 'manager-review';
type UserRole = 'user' | 'manager';

export interface SubmissionData {
  chartId: string; // Added to track which chart was submitted
  patientInfo: {
    memberId: string;
    name: string;
    dob: string;
    gender: string;
    lob: string;
    provider: string;
    npi: string;
  };
  aiHighlights: Array<{
    measure: string;
    dos: string;
    systolic: string;
    diastolic: string;
    page: number;
    selected: boolean;
  }>;
  formResults: {
    activeTab: 'inclusions' | 'exclusions' | 'none-found';
    telehealth: boolean;
    systolic: string;
    diastolic: string;
    dateOfService: string;
    notes: string;
    exclusionReasons: string[];
    exclusionDateOfService: string;
    noneFoundReasons: string[];
  };
  manualHighlights: Array<{
    id: string;
    pageNumber: number;
    lineIndex: number;
    startIndex: number;
    endIndex: number;
    text: string;
    timestamp: number;
  }>;
  submittedAt: string;
  submittedBy: string;
}

export default function App() {
  const [currentView, setCurrentView] = useState<View>('dashboard');
  const [userRole, setUserRole] = useState<UserRole>('user');
  const [selectedChartId, setSelectedChartId] = useState<string>('');
  
  // Store submitted reviews for manager access
  const [submittedReviews, setSubmittedReviews] = useState<Record<string, SubmissionData>>({});
  
  // Track which charts have been submitted (removed from user dashboard)
  const [submittedChartIds, setSubmittedChartIds] = useState<Set<string>>(new Set());
  
  // Current submission data for manager review
  const [currentSubmissionData, setCurrentSubmissionData] = useState<SubmissionData | null>(null);

  const handleNavigateToChart = (chartId: string) => {
    setSelectedChartId(chartId);
    if (userRole === 'manager') {
      // If manager clicks review, show the manager review page with actual submission data
      const submissionData = submittedReviews[chartId];
      if (submissionData) {
        setCurrentSubmissionData(submissionData);
        setCurrentView('manager-review');
      }
    } else {
      // Regular user goes to chart view
      setCurrentView('chart');
    }
  };

  const handleBackToDashboard = () => {
    setCurrentView('dashboard');
    setSelectedChartId('');
    setCurrentSubmissionData(null);
  };

  const handleSubmissionComplete = (data: SubmissionData) => {
    // Add the chart ID to the submission data
    const submissionWithChartId = {
      ...data,
      chartId: selectedChartId,
      submittedAt: new Date().toISOString(),
      submittedBy: 'Jane Chu'
    };
    
    // Store the submission for manager access
    setSubmittedReviews(prev => ({
      ...prev,
      [selectedChartId]: submissionWithChartId
    }));
    
    // Mark chart as submitted (removes from user dashboard)
    setSubmittedChartIds(prev => new Set([...prev, selectedChartId]));
    
    // Return to dashboard
    setCurrentView('dashboard');
    setSelectedChartId('');
  };

  const handleBackFromManagerReview = () => {
    setCurrentView('dashboard');
    setCurrentSubmissionData(null);
  };

  const handleRoleSwitch = (role: UserRole) => {
    setUserRole(role);
    setCurrentView('dashboard');
    setSelectedChartId('');
    setCurrentSubmissionData(null);
  };

  if (currentView === 'chart') {
    return (
      <ChartViewWrapper 
        chartId={selectedChartId}
        onBackToDashboard={handleBackToDashboard}
        onSubmissionComplete={handleSubmissionComplete}
      />
    );
  }

  if (currentView === 'manager-review' && currentSubmissionData) {
    return (
      <ManagerReview 
        submissionData={currentSubmissionData}
        onBackToDashboard={handleBackFromManagerReview}
      />
    );
  }

  return (
    <MedicalChartDashboard 
      onNavigateToChart={handleNavigateToChart}
      userRole={userRole}
      onRoleSwitch={handleRoleSwitch}
      submittedChartIds={submittedChartIds}
      submittedReviews={submittedReviews}
    />
  );
}