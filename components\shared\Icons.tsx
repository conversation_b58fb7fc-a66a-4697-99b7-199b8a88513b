import svgPaths from "../../imports/svg-alg61w3vn7";
import imgStellarusLogo from "figma:asset/abb0be6be146caf56aaaa0a625c81daa292fb52d.png";

export function Logo() {
  return (
    <div
      className="bg-card box-border content-stretch flex flex-row gap-3 items-center justify-start px-3 sm:px-5 py-2.5 relative shrink-0 w-40 sm:w-60"
      data-name="logo"
    >
      <div
        className="bg-center bg-cover bg-no-repeat h-[30px] sm:h-[37.397px] shrink-0 w-[120px] sm:w-[150px]"
        data-name="stellarus-logo"
        style={{ backgroundImage: `url('${imgStellarusLogo}')` }}
      />
    </div>
  );
}

export function IconUser() {
  return (
    <div className="relative shrink-0 size-5" data-name="icon_user">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 20 20"
      >
        <g id="profile-circle">
          <path
            d={svgPaths.p3aba0f00}
            id="Vector"
            stroke="var(--color-primary)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
          <path
            d={svgPaths.p3e3a1300}
            id="Vector_2"
            stroke="var(--color-primary)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
          <path
            d={svgPaths.p1246af00}
            id="Vector_3"
            stroke="var(--color-primary)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
        </g>
      </svg>
    </div>
  );
}

export function IconArrowDown() {
  return (
    <div className="relative shrink-0 size-6" data-name="icon_arrow_down">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 24 24"
      >
        <g id="icon_arrow_down">
          <path
            d={svgPaths.p242a98e0}
            id="Vector"
            stroke="var(--color-muted-foreground)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
        </g>
      </svg>
    </div>
  );
}

export function IconArrow() {
  return (
    <div className="relative shrink-0 size-5" data-name="icon_arrow">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 20 20"
      >
        <g id="icon_arrow">
          <path
            d={svgPaths.p732ed00}
            id="Vector"
            stroke="var(--color-accent)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
        </g>
      </svg>
    </div>
  );
}