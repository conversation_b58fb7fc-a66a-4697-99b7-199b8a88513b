import svgPaths from "./svg-alg61w3vn7";
import imgStellarusLogo from "figma:asset/abb0be6be146caf56aaaa0a625c81daa292fb52d.png";
import imgPdfImage from "figma:asset/46064f26e73f448836177c93cf7a688bda3922d4.png";

function Logo() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 items-center justify-start px-5 py-2.5 relative shrink-0 w-60"
      data-name="logo"
    >
      <div
        className="bg-center bg-cover bg-no-repeat h-[37.397px] shrink-0 w-[150px]"
        data-name="stellarus-logo"
        style={{ backgroundImage: `url('${imgStellarusLogo}')` }}
      />
    </div>
  );
}

function Frame840() {
  return (
    <div className="basis-0 box-border content-stretch flex flex-row gap-5 grow items-center justify-start min-h-px min-w-px p-0 relative shrink-0">
      <Logo />
    </div>
  );
}

function IconUser() {
  return (
    <div className="relative shrink-0 size-5" data-name="icon_user">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 20 20"
      >
        <g id="profile-circle">
          <path
            d={svgPaths.p3aba0f00}
            id="Vector"
            stroke="var(--stroke-0, #3870B8)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
          <path
            d={svgPaths.p3e3a1300}
            id="Vector_2"
            stroke="var(--stroke-0, #3870B8)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
          <path
            d={svgPaths.p1246af00}
            id="Vector_3"
            stroke="var(--stroke-0, #3870B8)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
          <g id="Vector_4" opacity="0"></g>
        </g>
      </svg>
    </div>
  );
}

function Base() {
  return (
    <div
      className="bg-[rgba(56,112,184,0.2)] box-border content-stretch flex flex-row gap-2 items-center justify-center overflow-clip p-[8px] relative rounded-[120px] shrink-0"
      data-name="base"
    >
      <IconUser />
    </div>
  );
}

function IconArrowDown() {
  return (
    <div className="relative shrink-0 size-6" data-name="icon_arrow_down">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 24 24"
      >
        <g id="icon_arrow_down">
          <path
            d={svgPaths.p242a98e0}
            id="Vector"
            stroke="var(--stroke-0, #809FB8)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
        </g>
      </svg>
    </div>
  );
}

function Frame838() {
  return (
    <div className="box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0">
      <div className="flex flex-col font-['Urbane:Light',_sans-serif] justify-end leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Jane Chu</p>
      </div>
      <Base />
      <IconArrowDown />
    </div>
  );
}

function MenuItem() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-5 items-center justify-start px-5 py-3 relative shrink-0"
      data-name="Menu Item"
    >
      <Frame838 />
    </div>
  );
}

function Frame839() {
  return (
    <div className="box-border content-stretch flex flex-row gap-4 items-center justify-end p-0 relative shrink-0">
      <MenuItem />
    </div>
  );
}

function Frame() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-row grow h-[46px] items-center justify-between min-h-px min-w-px p-0 relative shrink-0"
      data-name="frame"
    >
      <Frame840 />
      <Frame839 />
    </div>
  );
}

function Box() {
  return (
    <div
      className="basis-0 grow h-full min-h-px min-w-px relative shrink-0"
      data-name="box"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="bg-clip-padding border-0 border-[transparent] border-solid box-border content-stretch flex flex-row items-center justify-start px-[30px] py-3 relative size-full">
          <Frame />
        </div>
      </div>
    </div>
  );
}

function Menu() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-row grow items-center justify-start min-h-px min-w-px p-0 relative shrink-0 w-full"
      data-name="menu"
    >
      <Box />
    </div>
  );
}

function Topbar() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-col h-20 items-start justify-center p-0 relative shrink-0 w-[1440px]"
      data-name="Topbar"
    >
      <div
        aria-hidden="true"
        className="absolute border-[#f1f5f7] border-[0px_0px_1px] border-solid inset-0 pointer-events-none"
      />
      <Menu />
    </div>
  );
}

function Menu1() {
  return (
    <div
      className="box-border content-stretch flex flex-col items-start justify-start p-0 relative shrink-0 w-full"
      data-name="Menu"
    >
      <Topbar />
    </div>
  );
}

function IconArrow() {
  return (
    <div className="relative shrink-0 size-5" data-name="icon_arrow">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 20 20"
      >
        <g id="icon_arrow">
          <path
            d={svgPaths.p732ed00}
            id="Vector"
            stroke="var(--stroke-0, #0071BC)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
        </g>
      </svg>
    </div>
  );
}

function BackToHome() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-2 items-center justify-start p-0 relative shrink-0"
      data-name="Back-to-home"
    >
      <IconArrow />
      <div className="font-['Urbane:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#0071bc] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Back</p>
      </div>
    </div>
  );
}

function MemberId() {
  return (
    <div
      className="box-border content-stretch flex flex-col items-start justify-start leading-[0] not-italic pb-1 pt-0 px-0 relative shrink-0 text-left"
      data-name="member-id"
    >
      <div className="font-['Urbane:Demi_Bold',_sans-serif] mb-[-4px] relative shrink-0 text-[#17181a] text-[20px] w-full">
        <p className="block leading-[1.6]">55820474</p>
      </div>
      <div className="font-['Urbane:Medium',_sans-serif] mb-[-4px] relative shrink-0 text-[#547996] text-[12px] w-full">
        <p className="block leading-[20px]">Member ID</p>
      </div>
    </div>
  );
}

function Frame928() {
  return (
    <div className="box-border content-stretch flex flex-row gap-9 items-start justify-start p-0 relative shrink-0">
      <BackToHome />
      <MemberId />
    </div>
  );
}

function Frame927() {
  return (
    <div className="box-border content-stretch flex flex-col gap-12 items-start justify-end p-0 relative shrink-0">
      <Frame928 />
    </div>
  );
}

function Frame916() {
  return (
    <div className="box-border content-stretch flex flex-col items-start justify-start leading-[0] not-italic pb-1 pt-0 px-0 relative shrink-0 text-[12px] text-left">
      <div className="font-['Urbane:Demi_Bold',_sans-serif] mb-[-4px] relative shrink-0 text-[#17181a] w-full">
        <p className="block leading-[20px]">John Dey</p>
      </div>
      <div className="font-['Urbane:Medium',_sans-serif] mb-[-4px] relative shrink-0 text-[#547996] w-full">
        <p className="block leading-[20px]">Member</p>
      </div>
    </div>
  );
}

function Frame917() {
  return (
    <div className="box-border content-stretch flex flex-col items-start justify-start leading-[0] not-italic pb-1 pt-0 px-0 relative shrink-0 text-[12px] text-left w-[69px]">
      <div className="font-['Urbane:Demi_Bold',_sans-serif] mb-[-4px] relative shrink-0 text-[#17181a] w-full">
        <p className="block leading-[20px]">01/05/1972</p>
      </div>
      <div className="font-['Urbane:Medium',_sans-serif] mb-[-4px] relative shrink-0 text-[#547996] w-full">
        <p className="block leading-[20px]">DOB</p>
      </div>
    </div>
  );
}

function Frame918() {
  return (
    <div className="box-border content-stretch flex flex-col items-start justify-start leading-[0] not-italic pb-1 pt-0 px-0 relative shrink-0 text-[12px] text-left w-[39px]">
      <div
        className="font-['Urbane:Demi_Bold',_sans-serif] mb-[-4px] min-w-full relative shrink-0 text-[#17181a]"
        style={{ width: "min-content" }}
      >
        <p className="block leading-[20px]">M</p>
      </div>
      <div className="font-['Urbane:Medium',_sans-serif] mb-[-4px] relative shrink-0 text-[#547996] text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Gender</p>
      </div>
    </div>
  );
}

function Frame919() {
  return (
    <div className="box-border content-stretch flex flex-col items-start justify-start leading-[0] not-italic pb-1 pt-0 px-0 relative shrink-0 text-[12px] text-left w-[51px]">
      <div className="font-['Urbane:Demi_Bold',_sans-serif] mb-[-4px] relative shrink-0 text-[#17181a] w-full">
        <p className="block leading-[20px]">MAHMO</p>
      </div>
      <div className="font-['Urbane:Medium',_sans-serif] mb-[-4px] relative shrink-0 text-[#547996] w-full">
        <p className="block leading-[20px]">LOB</p>
      </div>
    </div>
  );
}

function Frame924() {
  return (
    <div className="box-border content-stretch flex flex-row gap-10 items-center justify-start p-0 relative shrink-0">
      <Frame916 />
      <Frame917 />
      <Frame918 />
      <Frame919 />
    </div>
  );
}

function Frame921() {
  return (
    <div className="box-border content-stretch flex flex-col items-start justify-start leading-[0] not-italic pb-1 pt-0 px-0 relative shrink-0 text-[12px] text-left w-[115px]">
      <div className="font-['Urbane:Demi_Bold',_sans-serif] mb-[-4px] relative shrink-0 text-[#17181a] w-full">
        <p className="block leading-[20px]">Nicolas Dejong</p>
      </div>
      <div className="font-['Urbane:Medium',_sans-serif] mb-[-4px] relative shrink-0 text-[#547996] w-full">
        <p className="block leading-[20px]">Provider</p>
      </div>
    </div>
  );
}

function Frame920() {
  return (
    <div className="box-border content-stretch flex flex-col items-start justify-start leading-[0] not-italic pb-1 pt-0 px-0 relative shrink-0 text-[12px] text-left w-[68px]">
      <div className="font-['Urbane:Demi_Bold',_sans-serif] mb-[-4px] relative shrink-0 text-[#17181a] w-full">
        <p className="block leading-[20px]">882716229</p>
      </div>
      <div className="font-['Urbane:Medium',_sans-serif] mb-[-4px] relative shrink-0 text-[#547996] w-full">
        <p className="block leading-[20px]">NPI</p>
      </div>
    </div>
  );
}

function Frame923() {
  return (
    <div className="box-border content-stretch flex flex-row gap-6 items-center justify-start p-0 relative shrink-0">
      <Frame921 />
      <Frame920 />
    </div>
  );
}

function Frame922() {
  return (
    <div className="box-border content-stretch flex flex-row gap-[55px] items-center justify-start p-0 relative shrink-0">
      <Frame924 />
      <Frame923 />
    </div>
  );
}

function Frame925() {
  return (
    <div className="box-border content-stretch flex flex-row gap-12 items-start justify-start p-0 relative shrink-0 w-full">
      <Frame927 />
      <Frame922 />
    </div>
  );
}

function Table1() {
  return (
    <div
      className="bg-[#ffffff] relative rounded-lg shrink-0 w-full"
      data-name="Table"
    >
      <div
        aria-hidden="true"
        className="absolute border border-[#f1f5f7] border-solid inset-0 pointer-events-none rounded-lg"
      />
      <div className="flex flex-col justify-center relative size-full">
        <div className="box-border content-stretch flex flex-col items-start justify-center p-[20px] relative w-full">
          <Frame925 />
        </div>
      </div>
    </div>
  );
}

function Demographics() {
  return (
    <div
      className="box-border content-stretch flex flex-col gap-5 items-start justify-start p-0 relative shrink-0 w-[1380px]"
      data-name="Demographics"
    >
      <Table1 />
    </div>
  );
}

function Pdf() {
  return (
    <div
      className="absolute h-[978px] left-px overflow-x-clip overflow-y-auto top-0 w-[871px]"
      data-name="PDF"
    >
      <div
        className="absolute bg-[#ffffff] h-[9.264px] left-[115.38px] top-[5.37px] w-[6.782px]"
        data-name="Highlight"
      />
      <div
        className="absolute bg-[#f9f9f9] h-[9.264px] left-[138px] top-[5.37px] w-[6.782px]"
        data-name="Highlight"
      />
      <div className="absolute font-['Arial:Regular',_sans-serif] leading-[0] left-[117.27px] not-italic text-[#000000] text-[9px] text-left text-nowrap top-[1.84px]">
        <p className="block leading-[20px] whitespace-pre">1</p>
      </div>
      <div className="absolute font-['Arial:Regular',_sans-serif] leading-[0] left-[138.14px] not-italic text-[#000000] text-[9px] text-left text-nowrap top-[1.84px]">
        <p className="block leading-[20px] whitespace-pre">10</p>
      </div>
    </div>
  );
}

function Pdf1() {
  return (
    <div
      className="bg-[#e8e8eb] h-[978px] overflow-clip relative shrink-0 w-[872px]"
      data-name="PDF"
    >
      <div
        className="absolute bg-[5.4%_100%] bg-no-repeat bg-size-[100%_100.39%] h-[537.769px] left-[0.56px] top-[0.25px] w-[871.443px]"
        data-name="PDF Image"
        style={{ backgroundImage: `url('${imgPdfImage}')` }}
      />
      <Pdf />
    </div>
  );
}

function ChartView() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-col h-[1018px] items-start justify-start p-[20px] relative rounded-lg shrink-0 w-[913px]"
      data-name="Chart-view"
    >
      <div
        aria-hidden="true"
        className="absolute border border-[#f1f5f7] border-solid inset-0 pointer-events-none rounded-lg"
      />
      <Pdf1 />
    </div>
  );
}

function Stack() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-2.5 items-center justify-start p-0 relative shrink-0"
      data-name="stack"
    >
      <div className="font-['Urbane:Demi_Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[20px] text-left text-nowrap">
        <p className="block leading-[32px] whitespace-pre">AI Highlights</p>
      </div>
    </div>
  );
}

function Head() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-5 items-center justify-start pb-3 pt-0 px-0 relative shrink-0 w-full"
      data-name="head"
    >
      <Stack />
    </div>
  );
}

function Frame913() {
  return (
    <div className="box-border content-stretch flex flex-col items-end justify-start p-0 relative shrink-0 w-full">
      <Head />
    </div>
  );
}

function TableItem64() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-10 items-center justify-start p-0 relative shrink-0"
      data-name="Table Item"
    >
      <div className="font-['Urbane:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Measure</p>
      </div>
    </div>
  );
}

function HeaderItem8() {
  return (
    <div className="h-10 relative shrink-0 w-full" data-name="header item">
      <div
        aria-hidden="true"
        className="absolute border-[#d9e1e7] border-[0px_0px_1px] border-solid inset-0 pointer-events-none"
      />
      <div className="relative size-full">
        <div className="box-border content-stretch flex flex-col gap-2.5 h-10 items-start justify-start px-2 py-0 relative w-full">
          <TableItem64 />
        </div>
      </div>
    </div>
  );
}

function IconText28() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0"
      data-name="icon text"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[16px] whitespace-pre">CBP</p>
      </div>
    </div>
  );
}

function TableItem65() {
  return (
    <div
      className="bg-[rgba(255,255,255,0.25)] h-10 relative shrink-0 w-full"
      data-name="Table Item"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 h-10 items-center justify-start px-2 py-2.5 relative w-full">
          <IconText28 />
        </div>
      </div>
    </div>
  );
}

function IconText29() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0"
      data-name="icon text"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[16px] whitespace-pre">CBP</p>
      </div>
    </div>
  );
}

function TableItem66() {
  return (
    <div
      className="bg-[#ffffff] h-10 relative shrink-0 w-full"
      data-name="Table Item"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 h-10 items-center justify-start px-2 py-2.5 relative w-full">
          <IconText29 />
        </div>
      </div>
    </div>
  );
}

function Column8() {
  return (
    <div
      className="box-border content-stretch flex flex-col items-start justify-start p-0 relative shrink-0"
      data-name="column"
    >
      <HeaderItem8 />
      <TableItem65 />
      {[...Array(2).keys()].map((_, i) => (
        <TableItem66 key={i} />
      ))}
    </div>
  );
}

function TableItem68() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-10 items-center justify-start p-0 relative shrink-0"
      data-name="Table Item"
    >
      <div className="font-['Urbane:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">DoS</p>
      </div>
    </div>
  );
}

function HeaderItem9() {
  return (
    <div className="h-10 relative shrink-0 w-full" data-name="header item">
      <div
        aria-hidden="true"
        className="absolute border-[#d9e1e7] border-[0px_0px_1px] border-solid inset-0 pointer-events-none"
      />
      <div className="relative size-full">
        <div className="box-border content-stretch flex flex-col gap-2.5 h-10 items-start justify-start px-2 py-0 relative w-full">
          <TableItem68 />
        </div>
      </div>
    </div>
  );
}

function IconText31() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0"
      data-name="icon text"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[16px] whitespace-pre">07/21/24</p>
      </div>
    </div>
  );
}

function TableItem69() {
  return (
    <div
      className="bg-[rgba(255,255,255,0.25)] h-10 relative shrink-0 w-full"
      data-name="Table Item"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 h-10 items-center justify-start px-2 py-2.5 relative w-full">
          <IconText31 />
        </div>
      </div>
    </div>
  );
}

function IconText32() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0"
      data-name="icon text"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[16px] whitespace-pre">07/21/24</p>
      </div>
    </div>
  );
}

function TableItem70() {
  return (
    <div
      className="bg-[#ffffff] h-10 relative shrink-0 w-full"
      data-name="Table Item"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 h-10 items-center justify-start px-2 py-2.5 relative w-full">
          <IconText32 />
        </div>
      </div>
    </div>
  );
}

function IconText33() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0"
      data-name="icon text"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[16px] whitespace-pre">05/21/24</p>
      </div>
    </div>
  );
}

function TableItem71() {
  return (
    <div
      className="bg-[#ffffff] h-10 relative shrink-0 w-full"
      data-name="Table Item"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 h-10 items-center justify-start px-2 py-2.5 relative w-full">
          <IconText33 />
        </div>
      </div>
    </div>
  );
}

function Column9() {
  return (
    <div
      className="box-border content-stretch flex flex-col items-start justify-start p-0 relative shrink-0"
      data-name="column"
    >
      <HeaderItem9 />
      <TableItem69 />
      <TableItem70 />
      <TableItem71 />
    </div>
  );
}

function TableItem72() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-10 items-center justify-start p-0 relative shrink-0"
      data-name="Table Item"
    >
      <div className="font-['Urbane:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Systolic</p>
      </div>
    </div>
  );
}

function HeaderItem10() {
  return (
    <div
      className="box-border content-stretch flex flex-col gap-2.5 h-10 items-start justify-start px-2 py-0 relative shrink-0"
      data-name="header item"
    >
      <div
        aria-hidden="true"
        className="absolute border-[#d9e1e7] border-[0px_0px_1px] border-solid inset-0 pointer-events-none"
      />
      <TableItem72 />
    </div>
  );
}

function IconText34() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0"
      data-name="icon text"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[16px] whitespace-pre">136</p>
      </div>
    </div>
  );
}

function TableItem73() {
  return (
    <div
      className="bg-[rgba(255,255,255,0.25)] h-10 relative shrink-0 w-full"
      data-name="Table Item"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 h-10 items-center justify-start px-2 py-2.5 relative w-full">
          <IconText34 />
        </div>
      </div>
    </div>
  );
}

function IconText35() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0 w-[22px]"
      data-name="icon text"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left w-[72px]">
        <p className="block leading-[16px]">140</p>
      </div>
    </div>
  );
}

function TableItem74() {
  return (
    <div
      className="bg-[#ffffff] h-10 relative shrink-0 w-full"
      data-name="Table Item"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 h-10 items-center justify-start px-2 py-2.5 relative w-full">
          <IconText35 />
        </div>
      </div>
    </div>
  );
}

function IconText36() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0 w-[22px]"
      data-name="icon text"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left w-[72px]">
        <p className="block leading-[16px]">150</p>
      </div>
    </div>
  );
}

function TableItem75() {
  return (
    <div
      className="bg-[#ffffff] h-10 relative shrink-0 w-full"
      data-name="Table Item"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 h-10 items-center justify-start px-2 py-2.5 relative w-full">
          <IconText36 />
        </div>
      </div>
    </div>
  );
}

function Column10() {
  return (
    <div
      className="box-border content-stretch flex flex-col items-start justify-start p-0 relative shrink-0"
      data-name="column"
    >
      <HeaderItem10 />
      <TableItem73 />
      <TableItem74 />
      <TableItem75 />
    </div>
  );
}

function TableItem76() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-10 items-center justify-start p-0 relative shrink-0"
      data-name="Table Item"
    >
      <div className="font-['Urbane:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Diastolic</p>
      </div>
    </div>
  );
}

function HeaderItem11() {
  return (
    <div
      className="box-border content-stretch flex flex-col gap-2.5 h-10 items-start justify-start px-2 py-0 relative shrink-0"
      data-name="header item"
    >
      <div
        aria-hidden="true"
        className="absolute border-[#d9e1e7] border-[0px_0px_1px] border-solid inset-0 pointer-events-none"
      />
      <TableItem76 />
    </div>
  );
}

function IconText37() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0"
      data-name="icon text"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[16px] whitespace-pre">82</p>
      </div>
    </div>
  );
}

function TableItem77() {
  return (
    <div
      className="bg-[rgba(255,255,255,0.25)] h-10 relative shrink-0 w-full"
      data-name="Table Item"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 h-10 items-center justify-start px-2 py-2.5 relative w-full">
          <IconText37 />
        </div>
      </div>
    </div>
  );
}

function IconText38() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0"
      data-name="icon text"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left w-4">
        <p className="block leading-[16px]">82</p>
      </div>
    </div>
  );
}

function TableItem78() {
  return (
    <div
      className="bg-[#ffffff] h-10 relative shrink-0 w-full"
      data-name="Table Item"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 h-10 items-center justify-start px-2 py-2.5 relative w-full">
          <IconText38 />
        </div>
      </div>
    </div>
  );
}

function IconText39() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0"
      data-name="icon text"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[16px] whitespace-pre">90</p>
      </div>
    </div>
  );
}

function TableItem79() {
  return (
    <div
      className="bg-[#ffffff] h-10 relative shrink-0 w-full"
      data-name="Table Item"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 h-10 items-center justify-start px-2 py-2.5 relative w-full">
          <IconText39 />
        </div>
      </div>
    </div>
  );
}

function Column11() {
  return (
    <div
      className="box-border content-stretch flex flex-col items-start justify-start p-0 relative shrink-0"
      data-name="column"
    >
      <HeaderItem11 />
      <TableItem77 />
      <TableItem78 />
      <TableItem79 />
    </div>
  );
}

function TableItem80() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-10 items-center justify-start p-0 relative shrink-0"
      data-name="Table Item"
    >
      <div className="font-['Urbane:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Page</p>
      </div>
    </div>
  );
}

function HeaderItem12() {
  return (
    <div className="h-10 relative shrink-0 w-full" data-name="header item">
      <div
        aria-hidden="true"
        className="absolute border-[#d9e1e7] border-[0px_0px_1px] border-solid inset-0 pointer-events-none"
      />
      <div className="relative size-full">
        <div className="box-border content-stretch flex flex-col gap-2.5 h-10 items-start justify-start px-2 py-0 relative w-full">
          <TableItem80 />
        </div>
      </div>
    </div>
  );
}

function IconText40() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0"
      data-name="icon text"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#0071bc] text-[0px] text-left text-nowrap">
        <p className="[text-decoration-line:underline] [text-decoration-skip-ink:none] [text-decoration-style:solid] [text-underline-position:from-font] block leading-[16px] text-[12px] whitespace-pre">
          2
        </p>
      </div>
    </div>
  );
}

function TableItem81() {
  return (
    <div
      className="bg-[rgba(255,255,255,0.25)] h-10 relative shrink-0 w-full"
      data-name="Table Item"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 h-10 items-center justify-start px-2 py-2.5 relative w-full">
          <IconText40 />
        </div>
      </div>
    </div>
  );
}

function IconText41() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0"
      data-name="icon text"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#0071bc] text-[0px] text-left text-nowrap">
        <p className="[text-decoration-line:underline] [text-decoration-skip-ink:none] [text-decoration-style:solid] [text-underline-position:from-font] block leading-[16px] text-[12px] whitespace-pre">
          2
        </p>
      </div>
    </div>
  );
}

function TableItem82() {
  return (
    <div
      className="bg-[#ffffff] h-10 relative shrink-0 w-full"
      data-name="Table Item"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 h-10 items-center justify-start px-2 py-2.5 relative w-full">
          <IconText41 />
        </div>
      </div>
    </div>
  );
}

function IconText42() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative shrink-0"
      data-name="icon text"
    >
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#0071bc] text-[0px] text-left text-nowrap">
        <p className="[text-decoration-line:underline] [text-decoration-skip-ink:none] [text-decoration-style:solid] [text-underline-position:from-font] block leading-[16px] text-[12px] whitespace-pre">
          7
        </p>
      </div>
    </div>
  );
}

function TableItem83() {
  return (
    <div
      className="bg-[#ffffff] h-10 relative shrink-0 w-full"
      data-name="Table Item"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 h-10 items-center justify-start px-2 py-2.5 relative w-full">
          <IconText42 />
        </div>
      </div>
    </div>
  );
}

function Column12() {
  return (
    <div
      className="box-border content-stretch flex flex-col items-start justify-start p-0 relative shrink-0"
      data-name="column"
    >
      <HeaderItem12 />
      <TableItem81 />
      <TableItem82 />
      <TableItem83 />
    </div>
  );
}

function TableItem84() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-10 items-center justify-start p-0 relative shrink-0 w-full"
      data-name="Table Item"
    >
      <div className="font-['Urbane:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[12px] text-left w-[53.541px]">
        <p className="block leading-[20px]">Include</p>
      </div>
    </div>
  );
}

function HeaderItem13() {
  return (
    <div className="relative shrink-0 w-full" data-name="header item">
      <div
        aria-hidden="true"
        className="absolute border-[#d9e1e7] border-[0px_0px_1px] border-solid inset-0 pointer-events-none"
      />
      <div className="relative size-full">
        <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start px-2 py-0 relative w-full">
          <TableItem84 />
        </div>
      </div>
    </div>
  );
}

function Check() {
  return (
    <div className="relative shrink-0 size-4" data-name="check">
      <div className="absolute bg-[#ffffff] left-0 rounded-[5px] size-4 top-0">
        <div
          aria-hidden="true"
          className="absolute border border-[#d9e1e7] border-solid inset-0 pointer-events-none rounded-[5px]"
        />
      </div>
    </div>
  );
}

function TableItem85() {
  return (
    <div
      className="bg-[#ffffff] h-10 relative shrink-0 w-full"
      data-name="Table Item"
    >
      <div className="flex flex-row items-center justify-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 h-10 items-center justify-center px-2 py-[18px] relative w-full">
          <Check />
        </div>
      </div>
    </div>
  );
}

function Check1() {
  return (
    <div className="relative shrink-0 size-4" data-name="check">
      <div className="absolute bg-[#ffffff] left-0 rounded-[5px] size-4 top-0">
        <div
          aria-hidden="true"
          className="absolute border border-[#d9e1e7] border-solid inset-0 pointer-events-none rounded-[5px]"
        />
      </div>
    </div>
  );
}

function TableItem86() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-10 items-center justify-start px-2 py-0 relative shrink-0"
      data-name="Table Item"
    >
      <Check1 />
    </div>
  );
}

function Column13() {
  return (
    <div
      className="box-border content-stretch flex flex-col items-center justify-center p-0 relative shrink-0 w-full"
      data-name="column"
    >
      <HeaderItem13 />
      <TableItem85 />
      {[...Array(2).keys()].map((_, i) => (
        <TableItem86 key={i} />
      ))}
    </div>
  );
}

function Column14() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-col grow items-start justify-start min-h-px min-w-px p-0 relative shrink-0"
      data-name="column"
    >
      <Column13 />
    </div>
  );
}

function Columns1() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-row grow items-start justify-start min-h-px min-w-px p-0 relative shrink-0"
      data-name="columns"
    >
      <Column8 />
      <Column9 />
      <Column10 />
      <Column11 />
      <Column12 />
      <Column14 />
    </div>
  );
}

function Table2() {
  return (
    <div
      className="box-border content-stretch flex flex-row items-start justify-start p-0 relative shrink-0 w-full"
      data-name="table"
    >
      <Columns1 />
    </div>
  );
}

function AiHighlights() {
  return (
    <div
      className="bg-[#ffffff] relative rounded-lg shrink-0 w-full"
      data-name="AI Highlights"
    >
      <div
        aria-hidden="true"
        className="absolute border border-[#f1f5f7] border-solid inset-0 pointer-events-none rounded-lg"
      />
      <div className="relative size-full">
        <div className="box-border content-stretch flex flex-col items-start justify-start p-[20px] relative w-full">
          <Frame913 />
          <Table2 />
        </div>
      </div>
    </div>
  );
}

function RightCorner() {
  return (
    <div
      className="basis-0 grow h-full min-h-px min-w-px shrink-0"
      data-name="right corner"
    />
  );
}

function Stack1() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-2.5 items-center justify-start p-0 relative shrink-0 w-full"
      data-name="stack"
    >
      <div className="font-['Urbane:Demi_Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[20px] text-left text-nowrap">
        <p className="block leading-[32px] whitespace-pre">Results</p>
      </div>
      <div className="basis-0 flex flex-row grow items-center self-stretch shrink-0">
        <RightCorner />
      </div>
    </div>
  );
}

function Frame831() {
  return (
    <div className="box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative rounded-md shrink-0">
      <div className="font-['Urbane:Demi_Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#3870b8] text-[14px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Inclusions</p>
      </div>
    </div>
  );
}

function MenuItem1() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-full items-center justify-start px-2 py-1 relative shrink-0"
      data-name="Menu Item"
    >
      <Frame831 />
    </div>
  );
}

function MenuItem2() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-4 h-full items-start justify-start p-0 relative shrink-0"
      data-name="menu item"
    >
      <div
        aria-hidden="true"
        className="absolute border-[#3870b8] border-[0px_0px_1px] border-solid inset-0 pointer-events-none"
      />
      <MenuItem1 />
    </div>
  );
}

function Frame832() {
  return (
    <div className="box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative rounded-md shrink-0">
      <div className="font-['Urbane:Demi_Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#547996] text-[14px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Exclusions</p>
      </div>
    </div>
  );
}

function MenuItem3() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-full items-center justify-start px-2 py-1 relative shrink-0"
      data-name="Menu Item"
    >
      <Frame832 />
    </div>
  );
}

function Frame833() {
  return (
    <div className="box-border content-stretch flex flex-row gap-3 items-center justify-start p-0 relative rounded-md shrink-0">
      <div className="font-['Urbane:Demi_Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#547996] text-[14px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">None found</p>
      </div>
    </div>
  );
}

function MenuItem4() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-3 h-full items-center justify-start px-2 py-1 relative shrink-0"
      data-name="Menu Item"
    >
      <Frame833 />
    </div>
  );
}

function MenuItem5() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-4 h-full items-start justify-start p-0 relative shrink-0"
      data-name="menu item"
    >
      <MenuItem3 />
      <MenuItem4 />
    </div>
  );
}

function Menu2() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-4 h-full items-center justify-start p-0 relative shrink-0"
      data-name="menu"
    >
      <MenuItem2 />
      <MenuItem5 />
    </div>
  );
}

function Box1() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-row grow items-center justify-start min-h-px min-w-px p-0 relative shrink-0"
      data-name="box"
    >
      <Menu2 />
    </div>
  );
}

function Bradcrumb() {
  return (
    <div className="h-10 relative shrink-0" data-name="bradcrumb">
      <div className="bg-clip-padding border-0 border-[transparent] border-solid box-border content-stretch flex flex-col h-10 items-start justify-center p-0 relative">
        <Box1 />
      </div>
    </div>
  );
}

function SecondMenu() {
  return (
    <div
      className="bg-[#ffffff] box-border content-stretch flex flex-row items-center justify-start pb-px pt-0 px-0 relative shrink-0 w-full"
      data-name="second menu"
    >
      <div
        aria-hidden="true"
        className="absolute border-[#d9e1e7] border-[0px_0px_1px] border-solid inset-0 pointer-events-none"
      />
      <Bradcrumb />
    </div>
  );
}

function Menu3() {
  return (
    <div
      className="box-border content-stretch flex flex-col h-[41px] items-start justify-start p-0 relative shrink-0 w-full"
      data-name="menu"
    >
      <SecondMenu />
    </div>
  );
}

function Frame929() {
  return (
    <div className="basis-0 box-border content-stretch flex flex-col gap-3 grow items-start justify-start min-h-px min-w-px p-0 relative shrink-0">
      <Stack1 />
      <Menu3 />
    </div>
  );
}

function Head1() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-5 items-center justify-start pb-3 pt-0 px-0 relative shrink-0 w-full"
      data-name="head"
    >
      <Frame929 />
    </div>
  );
}

function Check3() {
  return (
    <div className="relative shrink-0 size-4" data-name="check">
      <div className="absolute bg-[#ffffff] left-0 rounded-[5px] size-4 top-0">
        <div
          aria-hidden="true"
          className="absolute border border-[#d9e1e7] border-solid inset-0 pointer-events-none rounded-[5px]"
        />
      </div>
    </div>
  );
}

function Frame942() {
  return (
    <div className="box-border content-stretch flex flex-row gap-2 items-center justify-start p-0 relative shrink-0">
      <Check3 />
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#000000] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Teleheath</p>
      </div>
    </div>
  );
}

function Row() {
  return (
    <div
      className="bg-[#ffffff] relative rounded-md shrink-0 w-full"
      data-name="row"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 items-center justify-start px-3 py-2 relative w-full">
          <div className="basis-0 font-['Urbane:Light',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[#547996] text-[12px] text-left">
            <p className="block leading-[20px]">Value</p>
          </div>
        </div>
      </div>
    </div>
  );
}

function DropdownInclusion() {
  return (
    <div
      className="bg-[#ffffff] h-12 relative rounded-[10px] shrink-0 w-full"
      data-name="Dropdown_inclusion"
    >
      <div className="flex flex-col items-center justify-center overflow-clip relative size-full">
        <div className="box-border content-stretch flex flex-col h-12 items-center justify-center p-[4px] relative w-full">
          <Row />
        </div>
      </div>
      <div
        aria-hidden="true"
        className="absolute border border-[#d9e1e7] border-solid inset-0 pointer-events-none rounded-[10px]"
      />
    </div>
  );
}

function FormField() {
  return (
    <div
      className="box-border content-stretch flex flex-col gap-1 items-start justify-start p-0 relative shrink-0 w-28"
      data-name="Form field"
    >
      <DropdownInclusion />
    </div>
  );
}

function Frame938() {
  return (
    <div className="box-border content-stretch flex flex-col gap-1 items-start justify-start p-0 relative shrink-0">
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#547996] text-[10px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Systolic</p>
      </div>
      <FormField />
    </div>
  );
}

function Row1() {
  return (
    <div
      className="bg-[#ffffff] relative rounded-md shrink-0 w-full"
      data-name="row"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 items-center justify-start px-3 py-2 relative w-full">
          <div className="basis-0 font-['Urbane:Light',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[#547996] text-[12px] text-left">
            <p className="block leading-[20px]">Value</p>
          </div>
        </div>
      </div>
    </div>
  );
}

function DropdownInclusion1() {
  return (
    <div
      className="bg-[#ffffff] h-12 relative rounded-[10px] shrink-0 w-full"
      data-name="Dropdown_inclusion"
    >
      <div className="flex flex-col items-center justify-center overflow-clip relative size-full">
        <div className="box-border content-stretch flex flex-col h-12 items-center justify-center p-[4px] relative w-full">
          <Row1 />
        </div>
      </div>
      <div
        aria-hidden="true"
        className="absolute border border-[#d9e1e7] border-solid inset-0 pointer-events-none rounded-[10px]"
      />
    </div>
  );
}

function FormField1() {
  return (
    <div
      className="box-border content-stretch flex flex-col gap-1 items-start justify-start p-0 relative shrink-0 w-28"
      data-name="Form field"
    >
      <DropdownInclusion1 />
    </div>
  );
}

function Frame939() {
  return (
    <div className="box-border content-stretch flex flex-col gap-1 items-start justify-start p-0 relative shrink-0">
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#547996] text-[10px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Diastolic</p>
      </div>
      <FormField1 />
    </div>
  );
}

function IconInterface23() {
  return (
    <div className="relative shrink-0 size-6" data-name="icon_interface">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 24 24"
      >
        <g id="icon_interface">
          <path
            d={svgPaths.p393d4de0}
            id="Vector"
            stroke="var(--stroke-0, #547996)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
        </g>
      </svg>
    </div>
  );
}

function Row2() {
  return (
    <div
      className="bg-[#ffffff] relative rounded-md shrink-0 w-full"
      data-name="row"
    >
      <div className="flex flex-row items-center relative size-full">
        <div className="box-border content-stretch flex flex-row gap-3 items-center justify-start px-3 py-2 relative w-full">
          <div className="basis-0 font-['Urbane:Light',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[#547996] text-[12px] text-left">
            <p className="block leading-[20px]">MM/DD/YY</p>
          </div>
          <IconInterface23 />
        </div>
      </div>
    </div>
  );
}

function Calendar() {
  return (
    <div
      className="bg-[#ffffff] h-12 relative rounded-[10px] shrink-0 w-full"
      data-name="Calendar"
    >
      <div className="flex flex-col items-center justify-center overflow-clip relative size-full">
        <div className="box-border content-stretch flex flex-col h-12 items-center justify-center p-[4px] relative w-full">
          <Row2 />
        </div>
      </div>
      <div
        aria-hidden="true"
        className="absolute border border-[#d9e1e7] border-solid inset-0 pointer-events-none rounded-[10px]"
      />
    </div>
  );
}

function Frame937() {
  return (
    <div className="basis-0 box-border content-stretch flex flex-col gap-1 grow items-start justify-start min-h-px min-w-px p-0 relative shrink-0">
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#547996] text-[10px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Date of Service</p>
      </div>
      <Calendar />
    </div>
  );
}

function Frame940() {
  return (
    <div className="box-border content-stretch flex flex-row gap-1 items-start justify-start p-0 relative shrink-0 w-full">
      <Frame938 />
      <Frame939 />
      <Frame937 />
    </div>
  );
}

function Frame936() {
  return (
    <div className="box-border content-stretch flex flex-col gap-1 items-start justify-start p-0 relative shrink-0 w-full">
      <Frame940 />
    </div>
  );
}

function Text() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-row gap-0.5 grow items-start justify-start min-h-px min-w-px p-0 relative shrink-0 w-full"
      data-name="Text"
    >
      <div className="basis-0 flex flex-col font-['Urbane:Light',_sans-serif] grow h-[11.185px] justify-end leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[#547996] text-[12px] text-left">
        <p className="block leading-[16px]">Notes</p>
      </div>
    </div>
  );
}

function InputField() {
  return (
    <div
      className="bg-[#ffffff] h-[88px] relative rounded-lg shrink-0 w-full"
      data-name="Input Field"
    >
      <div
        aria-hidden="true"
        className="absolute border border-[#d9e1e7] border-solid inset-0 pointer-events-none rounded-lg"
      />
      <div className="relative size-full">
        <div className="box-border content-stretch flex flex-col gap-4 h-[88px] items-start justify-start pb-1 pt-2 px-3 relative w-full">
          <Text />
        </div>
      </div>
    </div>
  );
}

function TextAreaField() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-col gap-2 grow items-start justify-start min-h-px min-w-px p-0 relative shrink-0"
      data-name="Text Area Field"
    >
      <InputField />
    </div>
  );
}

function Notes() {
  return (
    <div
      className="box-border content-stretch flex flex-row items-start justify-start p-0 relative shrink-0 w-full"
      data-name="notes"
    >
      <TextAreaField />
    </div>
  );
}

function Frame941() {
  return (
    <div className="box-border content-stretch flex flex-col gap-1 items-start justify-start p-0 relative shrink-0 w-full">
      <div className="font-['Urbane:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#547996] text-[10px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Notes</p>
      </div>
      <Notes />
    </div>
  );
}

function Frame926() {
  return (
    <div className="box-border content-stretch flex flex-col gap-3 items-start justify-start p-0 relative shrink-0 w-full">
      <Frame942 />
      <Frame936 />
      <Frame941 />
    </div>
  );
}

function Notes1() {
  return (
    <div
      className="bg-[#ffffff] relative rounded-lg shrink-0 w-full"
      data-name="Notes"
    >
      <div
        aria-hidden="true"
        className="absolute border border-[#f1f5f7] border-solid inset-0 pointer-events-none rounded-lg"
      />
      <div className="flex flex-col justify-center relative size-full">
        <div className="box-border content-stretch flex flex-col items-start justify-center p-[20px] relative w-full">
          <Head1 />
          <Frame926 />
        </div>
      </div>
    </div>
  );
}

function ButtonBase() {
  return (
    <div
      className="bg-[#3870b8] box-border content-stretch flex flex-row gap-2 items-center justify-center overflow-clip px-4 py-2.5 relative rounded-lg shrink-0"
      data-name="_Button base"
    >
      <div className="font-['Urbane:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#ffffff] text-[12px] text-left text-nowrap">
        <p className="block leading-[20px] whitespace-pre">Submit</p>
      </div>
    </div>
  );
}

function SumbitButton() {
  return (
    <div
      className="box-border content-stretch flex flex-row items-end justify-end p-0 relative rounded-lg shrink-0"
      data-name="sumbit-button"
    >
      <ButtonBase />
    </div>
  );
}

function Sidebar() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-col gap-5 grow items-end justify-start min-h-px min-w-px p-0 relative shrink-0"
      data-name="Sidebar"
    >
      <AiHighlights />
      <Notes1 />
      <SumbitButton />
    </div>
  );
}

function Table3() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-5 items-start justify-start p-0 relative shrink-0 w-full"
      data-name="table"
    >
      <ChartView />
      <Sidebar />
    </div>
  );
}

function Content() {
  return (
    <div className="relative shrink-0 w-full" data-name="content">
      <div className="relative size-full">
        <div className="box-border content-stretch flex flex-col gap-5 items-start justify-start px-[30px] py-0 relative w-full">
          <div className="font-['Urbane:Demi_Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#17181a] text-[24px] text-left w-[222px]">
            <p className="block leading-[32px]">Chart Review</p>
          </div>
          <Demographics />
          <Table3 />
        </div>
      </div>
    </div>
  );
}

function Content1() {
  return (
    <div
      className="box-border content-stretch flex flex-col gap-5 items-start justify-start px-0 py-5 relative shrink-0 w-full"
      data-name="content"
    >
      <Content />
    </div>
  );
}

function Screen() {
  return (
    <div
      className="bg-[#f9fbfc] box-border content-stretch flex flex-col items-start justify-start p-0 relative shrink-0 w-[1440px]"
      data-name="screen"
    >
      <Menu1 />
      <Content1 />
    </div>
  );
}

export default function ChartViewV2() {
  return (
    <div
      className="bg-[#f6f6f6] box-border content-stretch flex flex-col items-start justify-start p-0 relative size-full"
      data-name="Chart view - v2"
    >
      <Screen />
    </div>
  );
}