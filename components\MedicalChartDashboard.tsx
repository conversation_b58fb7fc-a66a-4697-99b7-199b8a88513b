import { useState } from "react";
import svgPaths from "../imports/svg-xi7i9jsjra";
import imgStellarusLogo from "figma:asset/abb0be6be146caf56aaaa0a625c81daa292fb52d.png";
import { Button } from "./ui/button";
import { SubmissionData } from "../App";

type UserRole = 'user' | 'manager';

interface PatientChart {
  id: string;
  memberId: string;
  firstName: string;
  lastName: string;
  middleName: string;
  dob: string;
  lob: string;
  measure: string;
  review1: string;
  review2: string;
  assigned: string;
  isActive: boolean;
}

interface ManagerChart {
  id: string;
  memberId: string;
  firstName: string;
  lastName: string;
  middleName: string;
  dob: string;
  lob: string;
  measure: string;
  submittedBy: string;
  submittedAt: string;
  status: string;
  isActive: boolean;
}

const initialCharts: PatientChart[] = [
  {
    id: '1',
    memberId: '55820474',
    firstName: '<PERSON>',
    lastName: 'Dey',
    middleName: '',
    dob: '01/05/1972',
    lob: 'MA HMO',
    measure: 'CBP',
    review1: 'Jane Chu',
    review2: '-',
    assigned: '04/15/25 1:30pm',
    isActive: true
  },
  {
    id: '2',
    memberId: '302274401',
    firstName: 'Alma',
    lastName: 'Anders',
    middleName: 'G',
    dob: '12/15/1953',
    lob: 'MA HMO',
    measure: 'CBP',
    review1: 'Jane Chu',
    review2: '-',
    assigned: '04/15/25 1:30pm',
    isActive: false
  },
  {
    id: '3',
    memberId: '**********',
    firstName: 'Joanne',
    lastName: 'Smith',
    middleName: '',
    dob: '06/30/1951',
    lob: 'MA HMO',
    measure: 'CBP',
    review1: 'Jane Chu',
    review2: '-',
    assigned: '04/15/25 1:30pm',
    isActive: false
  }
];

function Logo() {
  return (
    <div
      className="bg-card box-border content-stretch flex flex-row gap-3 items-center justify-start px-3 sm:px-5 py-2.5 relative shrink-0 w-40 sm:w-60"
      data-name="logo"
    >
      <div
        className="bg-center bg-cover bg-no-repeat h-[30px] sm:h-[37.397px] shrink-0 w-[120px] sm:w-[150px]"
        data-name="stellarus-logo"
        style={{ backgroundImage: `url('${imgStellarusLogo}')` }}
      />
    </div>
  );
}

function IconUser() {
  return (
    <div className="relative shrink-0 size-5" data-name="icon_user">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 20 20"
      >
        <g id="profile-circle">
          <path
            d={svgPaths.p3aba0f00}
            id="Vector"
            stroke="var(--color-primary)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
          <path
            d={svgPaths.p3e3a1300}
            id="Vector_2"
            stroke="var(--color-primary)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
          <path
            d={svgPaths.p1246af00}
            id="Vector_3"
            stroke="var(--color-primary)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
        </g>
      </svg>
    </div>
  );
}

function IconArrowDown() {
  return (
    <div className="relative shrink-0 size-6" data-name="icon_arrow_down">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 24 24"
      >
        <g id="icon_arrow_down">
          <path
            d={svgPaths.p242a98e0}
            id="Vector"
            stroke="var(--color-muted-foreground)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
        </g>
      </svg>
    </div>
  );
}

function IconArrow() {
  return (
    <div className="relative shrink-0 size-5" data-name="icon_arrow">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 20 20"
      >
        <g id="icon_arrow">
          <path
            d={svgPaths.p1efb6980}
            id="Vector"
            stroke="var(--color-foreground)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
          <g id="Group">
            <path
              d={svgPaths.p70df800}
              id="Vector_2"
              stroke="var(--color-foreground)"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="1.5"
            />
            <path
              d={svgPaths.p14f8e060}
              id="Vector_3"
              stroke="var(--color-foreground)"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="1.5"
            />
            <path
              d={svgPaths.p7847000}
              id="Vector_4"
              stroke="var(--color-foreground)"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="1.5"
            />
            <path
              d={svgPaths.p29617600}
              id="Vector_5"
              stroke="var(--color-foreground)"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="1.5"
            />
          </g>
        </g>
      </svg>
    </div>
  );
}

interface UserDropdownProps {
  userRole: UserRole;
  onRoleSwitch: (role: UserRole) => void;
}

function UserDropdown({ userRole, onRoleSwitch }: UserDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  
  const currentUserName = userRole === 'manager' ? 'Manager' : 'Jane Chu';

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="box-border content-stretch flex flex-row gap-2 sm:gap-3 items-center justify-start p-0 relative shrink-0 cursor-pointer hover:opacity-80 transition-opacity touch-target"
      >
        <p className="text-foreground hidden sm:block">{currentUserName}</p>
        <div className="bg-primary/20 box-border content-stretch flex flex-row gap-2 items-center justify-center overflow-clip p-[8px] relative rounded-[120px] shrink-0">
          <IconUser />
        </div>
        <IconArrowDown />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown Menu */}
          <div className="absolute right-0 top-full mt-2 bg-card border border-border rounded-lg shadow-[var(--elevation-sm)] z-20 min-w-[200px]">
            <div className="py-2">
              <button
                onClick={() => {
                  setIsOpen(false);
                  // Add account view functionality here
                }}
                className="w-full text-left px-4 py-2 text-foreground hover:bg-secondary/50 transition-colors cursor-pointer touch-target"
              >
                View Account
              </button>
              <button
                onClick={() => {
                  setIsOpen(false);
                  // Add settings functionality here
                }}
                className="w-full text-left px-4 py-2 text-foreground hover:bg-secondary/50 transition-colors cursor-pointer touch-target"
              >
                Settings
              </button>
              <div className="border-t border-border my-1" />
              <button
                onClick={() => {
                  setIsOpen(false);
                  onRoleSwitch(userRole === 'user' ? 'manager' : 'user');
                }}
                className="w-full text-left px-4 py-2 text-foreground hover:bg-secondary/50 transition-colors cursor-pointer touch-target"
              >
                Switch to {userRole === 'user' ? 'Manager' : 'User'} Account
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
}

interface MedicalChartDashboardProps {
  onNavigateToChart: (chartId: string) => void;
  userRole: UserRole;
  onRoleSwitch: (role: UserRole) => void;
  submittedChartIds: Set<string>;
  submittedReviews: Record<string, SubmissionData>;
}

export default function MedicalChartDashboard({ 
  onNavigateToChart, 
  userRole, 
  onRoleSwitch, 
  submittedChartIds,
  submittedReviews
}: MedicalChartDashboardProps) {
  const [charts, setCharts] = useState<PatientChart[]>(initialCharts);
  const [refreshing, setRefreshing] = useState(false);
  
  // Review button hover states
  const [reviewButtonStates, setReviewButtonStates] = useState<Record<string, string>>({});

  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  const handleReviewChart = (chartId: string) => {
    if (userRole === 'manager') {
      // Manager can review submitted charts
      if (submittedReviews[chartId]) {
        onNavigateToChart(chartId);
      }
    } else {
      // Regular user can only review charts that haven't been submitted
      const chart = charts.find(c => c.id === chartId);
      if (chart && chart.isActive && !submittedChartIds.has(chartId)) {
        onNavigateToChart(chartId);
      }
    }
  };

  // Helper function to format submission date for display
  const formatSubmissionDate = (isoString: string) => {
    const date = new Date(isoString);
    return date.toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: '2-digit',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  // Create manager charts from submitted reviews
  const managerCharts: ManagerChart[] = Object.entries(submittedReviews).map(([chartId, submission]) => {
    const originalChart = initialCharts.find(c => c.id === chartId);
    return {
      id: chartId,
      memberId: submission.patientInfo.memberId,
      firstName: originalChart?.firstName || '',
      lastName: originalChart?.lastName || '',
      middleName: originalChart?.middleName || '',
      dob: submission.patientInfo.dob,
      lob: submission.patientInfo.lob,
      measure: 'CBP',
      submittedBy: submission.submittedBy,
      submittedAt: formatSubmissionDate(submission.submittedAt),
      status: 'Pending Review',
      isActive: true // All submitted reviews are available for manager review
    };
  });

  // Filter charts based on user role
  let currentCharts: (PatientChart | ManagerChart)[] = [];
  
  if (userRole === 'manager') {
    currentCharts = managerCharts;
  } else {
    // For regular users, filter out submitted charts
    currentCharts = charts.filter(chart => !submittedChartIds.has(chart.id));
  }

  const tableTitle = userRole === 'manager' ? 'Submitted reviews' : 'Assigned charts';

  return (
    <div className="bg-background box-border content-stretch flex flex-col items-start justify-start p-0 relative size-full min-h-screen">
      <div className="bg-background box-border content-stretch flex flex-col min-h-screen items-start justify-start p-0 relative shrink-0 w-full">
        {/* Top Navigation */}
        <div className="bg-card box-border content-stretch flex flex-col h-16 sm:h-20 items-start justify-center p-0 relative shrink-0 w-full">
          <div aria-hidden="true" className="absolute border-b border-border inset-0 pointer-events-none" />
          <div className="basis-0 box-border content-stretch flex flex-row grow items-center justify-start min-h-px min-w-px p-0 relative shrink-0 w-full">
            <div className="basis-0 grow h-full min-h-px min-w-px relative shrink-0">
              <div className="flex flex-row items-center relative size-full">
                <div className="bg-clip-padding border-0 border-[transparent] border-solid box-border content-stretch flex flex-row items-center justify-start px-4 sm:px-[30px] py-3 relative size-full">
                  <div className="basis-0 box-border content-stretch flex flex-row gap-3 sm:gap-5 grow h-[46px] items-center justify-between min-h-px min-w-px p-0 relative shrink-0">
                    <div className="basis-0 box-border content-stretch flex flex-row gap-3 sm:gap-5 grow items-center justify-start min-h-px min-w-px p-0 relative shrink-0">
                      <Logo />
                    </div>
                    <div className="box-border content-stretch flex flex-row gap-2 sm:gap-4 items-center justify-end p-0 relative shrink-0">
                      <div className="box-border content-stretch flex flex-row gap-3 sm:gap-5 items-center justify-start px-2 sm:px-5 py-3 relative shrink-0">
                        <UserDropdown userRole={userRole} onRoleSwitch={onRoleSwitch} />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="basis-0 box-border content-stretch flex flex-col gap-4 sm:gap-5 grow items-start justify-start min-h-px min-w-px px-0 py-4 sm:py-5 relative shrink-0 w-full">
          <div className="relative shrink-0 w-full">
            <div className="relative size-full">
              <div className="box-border content-stretch flex flex-col gap-4 sm:gap-5 items-start justify-start px-4 sm:px-[30px] py-0 relative w-full">
                <h1 className="text-foreground">Dashboard</h1>
                
                {/* Chart Table */}
                <div className="box-border content-stretch flex flex-col gap-4 sm:gap-5 items-start justify-start p-0 relative shrink-0 w-full">
                  <div className="bg-card relative rounded-lg shrink-0 w-full shadow-[var(--elevation-sm)]">
                    <div aria-hidden="true" className="absolute border border-border inset-0 pointer-events-none rounded-lg" />
                    <div className="flex flex-col justify-center relative size-full">
                      <div className="box-border content-stretch flex flex-col items-start justify-center p-4 sm:p-[20px] relative w-full">
                        
                        {/* Table Header */}
                        <div className="box-border content-stretch flex flex-col sm:flex-row gap-3 sm:gap-5 items-start sm:items-center justify-start pb-4 sm:pb-5 pt-0 px-0 relative shrink-0 w-full">
                          <div className="basis-0 box-border content-stretch flex flex-row gap-2.5 grow items-center justify-start min-h-px min-w-px p-0 relative shrink-0">
                            <h1 className="text-foreground">{tableTitle}</h1>
                          </div>
                          <Button 
                            variant="outline"
                            onClick={handleRefresh}
                            disabled={refreshing}
                            className="bg-card border-border hover:bg-secondary w-full sm:w-auto"
                          >
                            <IconArrow />
                            <span>{refreshing ? 'Refreshing...' : 'Refresh charts'}</span>
                          </Button>
                        </div>

                        {/* Empty State */}
                        {currentCharts.length === 0 && (
                          <div className="flex flex-col items-center justify-center py-12 w-full">
                            <p className="text-muted-foreground text-center">
                              {userRole === 'manager' 
                                ? 'No submitted reviews to display' 
                                : 'No charts available for review'
                              }
                            </p>
                          </div>
                        )}

                        {/* Table - Responsive Design */}
                        {currentCharts.length > 0 && (
                          <div className="w-full overflow-x-auto">
                            {/* Mobile/Tablet Card View */}
                            <div className="block lg:hidden space-y-4">
                              {currentCharts.map((chart) => (
                                <div key={chart.id} className="bg-secondary/30 p-4 rounded-lg space-y-3">
                                  <div className="flex justify-between items-start">
                                    <div>
                                      <p className="text-foreground font-[var(--font-weight-demi-bold)]">{chart.firstName} {chart.lastName}</p>
                                      <p className="text-muted-foreground text-xs">ID: {chart.memberId}</p>
                                    </div>
                                    <button
                                      onClick={() => handleReviewChart(chart.id)}
                                      disabled={!chart.isActive || (userRole === 'user' && submittedChartIds.has(chart.id))}
                                      onMouseEnter={() => setReviewButtonStates(prev => ({ ...prev, [chart.id]: 'hover' }))}
                                      onMouseLeave={() => setReviewButtonStates(prev => ({ ...prev, [chart.id]: 'default' }))}
                                      className={`px-4 py-2 rounded-lg transition-colors text-sm ${
                                        chart.isActive && !(userRole === 'user' && submittedChartIds.has(chart.id))
                                          ? 'bg-primary hover:bg-[#468CE7] cursor-pointer text-primary-foreground'
                                          : 'bg-muted cursor-not-allowed opacity-60 text-muted-foreground'
                                      }`}
                                    >
                                      Review
                                    </button>
                                  </div>
                                  <div className="grid grid-cols-2 gap-2 text-xs">
                                    <div>
                                      <span className="text-muted-foreground">DOB: </span>
                                      <span className="text-foreground">{chart.dob}</span>
                                    </div>
                                    <div>
                                      <span className="text-muted-foreground">LOB: </span>
                                      <span className="text-foreground">{chart.lob}</span>
                                    </div>
                                    <div>
                                      <span className="text-muted-foreground">Measure: </span>
                                      <span className="text-foreground">{chart.measure}</span>
                                    </div>
                                    {userRole === 'manager' ? (
                                      <>
                                        <div>
                                          <span className="text-muted-foreground">Submitted By: </span>
                                          <span className="text-foreground">{(chart as ManagerChart).submittedBy}</span>
                                        </div>
                                        <div>
                                          <span className="text-muted-foreground">Status: </span>
                                          <span className="text-foreground">{(chart as ManagerChart).status}</span>
                                        </div>
                                        <div>
                                          <span className="text-muted-foreground">Submitted At: </span>
                                          <span className="text-foreground">{(chart as ManagerChart).submittedAt}</span>
                                        </div>
                                      </>
                                    ) : (
                                      <div>
                                        <span className="text-muted-foreground">Assigned: </span>
                                        <span className="text-foreground">{(chart as PatientChart).assigned}</span>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>

                            {/* Desktop Table View */}
                            <div className="hidden lg:block">
                              <div className="box-border content-stretch flex flex-row items-start justify-start p-0 relative shrink-0 w-full min-w-[1200px]">
                                <div className="basis-0 box-border content-stretch flex flex-row grow items-start justify-start min-h-px min-w-px p-0 relative shrink-0">
                                  
                                  {/* Member ID Column */}
                                  <div className="basis-0 box-border content-stretch flex flex-col flex-1 items-start justify-start min-h-px min-w-px p-0 relative shrink-0">
                                    <div className="relative shrink-0 w-full">
                                      <div aria-hidden="true" className="absolute border-b border-border inset-0 pointer-events-none" />
                                      <div className="relative size-full">
                                        <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start px-3 py-0 relative w-full">
                                          <div className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start p-0 relative shrink-0">
                                            <h3 className="text-foreground">Member ID</h3>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    
                                    {/* Table Data Rows */}
                                    {currentCharts.map((chart) => (
                                      <div key={chart.id} className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-[13px] relative shrink-0">
                                        <p className="text-foreground">{chart.memberId}</p>
                                      </div>
                                    ))}
                                  </div>

                                  {/* First Name Column */}
                                  <div className="box-border content-stretch flex flex-col flex-1 items-start justify-start p-0 relative shrink-0">
                                    <div className="relative shrink-0 w-full">
                                      <div aria-hidden="true" className="absolute border-b border-border inset-0 pointer-events-none" />
                                      <div className="relative size-full">
                                        <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start px-3 py-0 relative w-full">
                                          <div className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start p-0 relative shrink-0">
                                            <h3 className="text-foreground">First name</h3>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    {currentCharts.map((chart) => (
                                      <div key={chart.id} className="bg-card box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-2.5 relative shrink-0">
                                        <p className="text-foreground">{chart.firstName}</p>
                                      </div>
                                    ))}
                                  </div>

                                  {/* Last Name Column */}
                                  <div className="box-border content-stretch flex flex-col flex-1 items-start justify-start p-0 relative shrink-0">
                                    <div className="relative shrink-0 w-full">
                                      <div aria-hidden="true" className="absolute border-b border-border inset-0 pointer-events-none" />
                                      <div className="relative size-full">
                                        <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start px-3 py-0 relative w-full">
                                          <div className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start p-0 relative shrink-0">
                                            <h3 className="text-foreground">Last name</h3>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    {currentCharts.map((chart) => (
                                      <div key={chart.id} className="bg-card box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-2.5 relative shrink-0">
                                        <p className="text-foreground">{chart.lastName}</p>
                                      </div>
                                    ))}
                                  </div>

                                  {/* Middle Name Column */}
                                  <div className="box-border content-stretch flex flex-col flex-1 items-start justify-start p-0 relative shrink-0">
                                    <div className="relative shrink-0 w-full">
                                      <div aria-hidden="true" className="absolute border-b border-border inset-0 pointer-events-none" />
                                      <div className="relative size-full">
                                        <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start px-3 py-0 relative w-full">
                                          <div className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start p-0 relative shrink-0">
                                            <h3 className="text-foreground">Middle name</h3>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    {currentCharts.map((chart) => (
                                      <div key={chart.id} className="bg-card box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-2.5 relative shrink-0">
                                        <p className="text-foreground">{chart.middleName || ''}</p>
                                      </div>
                                    ))}
                                  </div>

                                  {/* DOB Column */}
                                  <div className="box-border content-stretch flex flex-col flex-1 items-start justify-start p-0 relative shrink-0">
                                    <div className="relative shrink-0 w-full">
                                      <div aria-hidden="true" className="absolute border-b border-border inset-0 pointer-events-none" />
                                      <div className="relative size-full">
                                        <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start px-3 py-0 relative w-full">
                                          <div className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start p-0 relative shrink-0">
                                            <h3 className="text-foreground">DOB</h3>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    {currentCharts.map((chart) => (
                                      <div key={chart.id} className="bg-card box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-2.5 relative shrink-0">
                                        <p className="text-foreground">{chart.dob}</p>
                                      </div>
                                    ))}
                                  </div>

                                  {/* LOB Column */}
                                  <div className="box-border content-stretch flex flex-col flex-1 items-start justify-start p-0 relative shrink-0">
                                    <div className="relative shrink-0 w-full">
                                      <div aria-hidden="true" className="absolute border-b border-border inset-0 pointer-events-none" />
                                      <div className="relative size-full">
                                        <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start px-3 py-0 relative w-full">
                                          <div className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start p-0 relative shrink-0">
                                            <h3 className="text-foreground">LOB</h3>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    {currentCharts.map((chart) => (
                                      <div key={chart.id} className="bg-card box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-2.5 relative shrink-0">
                                        <p className="text-foreground">{chart.lob}</p>
                                      </div>
                                    ))}
                                  </div>

                                  {/* Measure Column */}
                                  <div className="box-border content-stretch flex flex-col flex-1 items-start justify-start p-0 relative shrink-0">
                                    <div className="relative shrink-0 w-full">
                                      <div aria-hidden="true" className="absolute border-b border-border inset-0 pointer-events-none" />
                                      <div className="relative size-full">
                                        <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start px-3 py-0 relative w-full">
                                          <div className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start p-0 relative shrink-0">
                                            <h3 className="text-foreground">Measure</h3>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    {currentCharts.map((chart) => (
                                      <div key={chart.id} className="bg-card h-[68px] relative shrink-0 w-full">
                                        <div className="flex flex-row items-center relative size-full">
                                          <div className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-2.5 relative w-full">
                                            <p className="text-foreground">{chart.measure}</p>
                                          </div>
                                        </div>
                                      </div>
                                    ))}
                                  </div>

                                  {/* Dynamic Column based on user role */}
                                  {userRole === 'manager' ? (
                                    <>
                                      {/* Submitted By Column */}
                                      <div className="box-border content-stretch flex flex-col flex-1 items-start justify-start p-0 relative shrink-0">
                                        <div className="relative shrink-0 w-full">
                                          <div aria-hidden="true" className="absolute border-b border-border inset-0 pointer-events-none" />
                                          <div className="relative size-full">
                                            <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start px-3 py-0 relative w-full">
                                              <div className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start p-0 relative shrink-0">
                                                <h3 className="text-foreground">Submitted By</h3>
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                        {managerCharts.map((chart) => (
                                          <div key={chart.id} className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-[13px] relative shrink-0">
                                            <p className="text-foreground">{chart.submittedBy}</p>
                                          </div>
                                        ))}
                                      </div>

                                      {/* Status Column */}
                                      <div className="box-border content-stretch flex flex-col flex-1 items-start justify-start p-0 relative shrink-0">
                                        <div className="relative shrink-0 w-full">
                                          <div aria-hidden="true" className="absolute border-b border-border inset-0 pointer-events-none" />
                                          <div className="relative size-full">
                                            <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start px-3 py-0 relative w-full">
                                              <div className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start p-0 relative shrink-0">
                                                <h3 className="text-foreground">Status</h3>
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                        {managerCharts.map((chart) => (
                                          <div key={chart.id} className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-0 relative shrink-0">
                                            <p className="text-foreground">{chart.status}</p>
                                          </div>
                                        ))}
                                      </div>
                                    </>
                                  ) : (
                                    <>
                                      {/* Review 1 Column */}
                                      <div className="box-border content-stretch flex flex-col flex-1 items-start justify-start p-0 relative shrink-0">
                                        <div className="relative shrink-0 w-full">
                                          <div aria-hidden="true" className="absolute border-b border-border inset-0 pointer-events-none" />
                                          <div className="relative size-full">
                                            <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start px-3 py-0 relative w-full">
                                              <div className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start p-0 relative shrink-0">
                                                <h3 className="text-foreground">Review 1</h3>
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                        {(currentCharts as PatientChart[]).map((chart) => (
                                          <div key={chart.id} className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-[13px] relative shrink-0">
                                            <p className="text-foreground">{chart.review1}</p>
                                          </div>
                                        ))}
                                      </div>

                                      {/* Review 2 Column */}
                                      <div className="box-border content-stretch flex flex-col flex-1 items-start justify-start p-0 relative shrink-0">
                                        <div className="relative shrink-0 w-full">
                                          <div aria-hidden="true" className="absolute border-b border-border inset-0 pointer-events-none" />
                                          <div className="relative size-full">
                                            <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start px-3 py-0 relative w-full">
                                              <div className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start p-0 relative shrink-0">
                                                <h3 className="text-foreground">Review 2</h3>
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                        {(currentCharts as PatientChart[]).map((chart) => (
                                          <div key={chart.id} className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start px-3 py-0 relative shrink-0">
                                            <p className="text-foreground">{chart.review2}</p>
                                          </div>
                                        ))}
                                      </div>
                                    </>
                                  )}

                                  {/* Action Column */}
                                  <div className="basis-0 box-border content-stretch flex flex-col flex-1 items-start justify-start min-h-px min-w-px p-0 relative shrink-0">
                                    <div className="relative shrink-0 w-full">
                                      <div aria-hidden="true" className="absolute border-b border-border inset-0 pointer-events-none" />
                                      <div className="relative size-full">
                                        <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start px-3 py-0 relative w-full">
                                          <div className="box-border content-stretch flex flex-row gap-3 h-[68px] items-center justify-start p-0 relative shrink-0">
                                            <h3 className="text-foreground">Action</h3>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    {currentCharts.map((chart) => (
                                      <div key={chart.id} className="box-border content-stretch flex flex-row h-[68px] items-center justify-start px-3 py-0 relative shrink-0">
                                        <button
                                          onClick={() => handleReviewChart(chart.id)}
                                          disabled={!chart.isActive || (userRole === 'user' && submittedChartIds.has(chart.id))}
                                          onMouseEnter={() => setReviewButtonStates(prev => ({ ...prev, [chart.id]: 'hover' }))}
                                          onMouseLeave={() => setReviewButtonStates(prev => ({ ...prev, [chart.id]: 'default' }))}
                                          className={`px-4 py-2 rounded-lg transition-colors ${
                                            chart.isActive && !(userRole === 'user' && submittedChartIds.has(chart.id))
                                              ? 'bg-primary hover:bg-[#468CE7] cursor-pointer text-primary-foreground'
                                              : 'bg-muted cursor-not-allowed opacity-60 text-muted-foreground'
                                          }`}
                                        >
                                          Review
                                        </button>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}